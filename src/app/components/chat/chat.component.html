<div class="chat-container" [class.collapsed]="isCollapsed">
  <div class="chat-header" (click)="toggleChat()">
    <span>Chat</span>
  </div>

  <div class="chat-body" *ngIf="!isCollapsed">
    <div class="chat-messages">
      <div *ngFor="let message of messages" class="message">
        <strong>{{ message.sender }}:</strong> {{ message.text }}
      </div>
    </div>

    <div class="chat-input">
      <input type="text" [(ngModel)]="newMessage" placeholder="Tapez un message..." (keydown.enter)="sendMessage()" />
      <button (click)="sendMessage()">Envoyer</button>
    </div>
  </div>
</div>
