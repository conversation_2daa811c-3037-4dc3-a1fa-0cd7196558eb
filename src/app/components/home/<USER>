<div class="view">
  <div class="left">
    <div class="spectro"></div>
      <h1 class="title-left">SERVEUR</h1>
      <button class="btn-left" routerLink="/loading/">Connexion</button>
  </div>
  <div class="divider"></div>
  <div class="right">
    <div class="sandbox"></div>
    <h1 class="title-right">SANDBOX</h1>
    <button class="btn-right" routerLink="/sandbox/">Sandbox</button>
  </div>
</div>

<div class="welcome-message">
  <div class="welcome-header">Bienvenue</div>
  <div class="welcome-info"> {{this.uniqueId}} </div>
</div>
