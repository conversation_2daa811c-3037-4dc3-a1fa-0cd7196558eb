import { Injectable } from '@angular/core';
import {HttpClient, HttpParams} from "@angular/common/http";
import {BehaviorSubject, Observable} from "rxjs";
import {Annotation, Rectangle, RectangleHelper} from '../components/spectro/spectro.component';
import {IUser} from "./user-service.service";
import {ClickstreamEvent, ReflectionTimeData, AnnotationSession} from '../models/clickstream.models';

export interface RectangleDTO {
  frequence_debut: number | undefined;
  frequence_fin: number | undefined;
  temps_debut: number | undefined;
  temps_fin: number | undefined;
  type_bruiteur: string | undefined;
  confiance: number | undefined;
  name_annoteur: string | null;
  name_image: string | null;

}

@Injectable({
  providedIn: 'root'
})
export class AnnotationService {
  private apiUrl = 'http://localhost:8000/annotations';  // URL de votre API FastAPI
  private apiURlAllAnnotation = 'http://localhost:8000/annotation_user_spectro'
  private APIURLResponse = 'http://localhost:8000/response/?name_spectro'

  // Clickstream API endpoints
  private clickstreamApiUrl = 'http://localhost:8000/clickstream';
  private reflectionTimeApiUrl = 'http://localhost:8000/reflection-time';

  private visibleSubject = new BehaviorSubject<boolean>(false);
  isAnnotationCreatorVisibile$ = this.visibleSubject.asObservable();


  constructor(private http: HttpClient) { }

  private formatTime(milisecond?: number): string {
    if (!milisecond) return '00:00:00';
    const date = new Date(milisecond);
    return date.toISOString().substring(11,19);
  }

  public mapDTO(rectangles: Rectangle): RectangleDTO {
    return {
      frequence_debut: rectangles.fLeft,
      frequence_fin: rectangles.fRight,
      temps_debut: rectangles.tBottom,
      temps_fin: rectangles.tTop,
      type_bruiteur: rectangles.bruiteur,
      confiance: rectangles.trust,
      name_annoteur: rectangles.userName,
      name_image: rectangles.imageName
    };
  }

  // Envoi des rectangles vers l'API
  addRectangles(rectangles: Rectangle): Observable<any> {
    const rectangleDTO = this.mapDTO(rectangles);
    return this.http.post(this.apiUrl, rectangleDTO);
  }

  // Récupérer tous les rectangles depuis l'API
  getRectangles(nameUser: string | null, nameImage: string | null): Observable<RectangleHelper[]> {
    return this.http.get<RectangleHelper[]>(`${this.apiURlAllAnnotation}/?name_annoteur=${nameUser}&spectro_name=${nameImage}`);
  }

  getResponseAnnotation(nameImage: string | null): Observable<RectangleHelper[]> {
    return this.http.get<RectangleHelper[]>(`${this.APIURLResponse}=${nameImage}`);
  }

  setAnnotationCreatorVisibile(trueOrFalse: boolean) {
    console.log("setAnnotationCreatorVisibile : ", trueOrFalse);
    this.visibleSubject.next(trueOrFalse);
  }

  getValueAnnotationVisibleOrNot() {
    return this.visibleSubject.getValue();
  }

  deleteAnnotation(imageName: string , userName : string, t_debut: number, t_fin: number, f_debut: number, f_end: number): Observable<any> {
    let params = new HttpParams()
      .set('image_name', imageName!)
      .set('user_name', userName!)
      .set('t_start', t_debut!)
      .set('t_end', t_fin!)
      .set('f_start', f_debut!)
      .set('f_end', f_end!)

    return this.http.delete(`${this.apiUrl}/delete?${params}`, {})
  }

  updateAnnotation(rectangleDTO: {
    frequence_debut: number | undefined;
    image_name: string | null;
    user_name: string | null;
    frequence_fin: number | undefined;
    new_type_buiteur: string;
    new_confiance: number;
    temps_debut: number | undefined;
    temps_fin: number | undefined
  }) {
    let params = new HttpParams()
      .set('image_name', rectangleDTO.image_name!)
      .set('user_name', rectangleDTO.user_name!)
      .set('temps_debut', rectangleDTO.temps_debut!)
      .set('temps_fin', rectangleDTO.temps_fin!)
      .set('frequence_debut', rectangleDTO.frequence_debut!)
      .set('frequence_fin', rectangleDTO.frequence_fin!)
      .set('new_type_buiteur', rectangleDTO.new_type_buiteur!)
      .set('new_confiance', rectangleDTO.new_confiance!)

    return this.http.put(`${this.apiUrl}/modify?${params}`, {});
  }

  // Clickstream and reflection time methods
  submitClickstreamEvents(events: ClickstreamEvent[]): Observable<any> {
    return this.http.post(`${this.clickstreamApiUrl}/events`, { events });
  }

  submitReflectionTimeData(reflectionData: ReflectionTimeData): Observable<any> {
    return this.http.post(`${this.reflectionTimeApiUrl}/data`, reflectionData);
  }

  submitAnnotationSession(session: AnnotationSession): Observable<any> {
    return this.http.post(`${this.clickstreamApiUrl}/session`, session);
  }

  getReflectionAnalytics(userId: string, spectroName?: string): Observable<any> {
    const params = spectroName ? `?userId=${userId}&spectroName=${spectroName}` : `?userId=${userId}`;
    return this.http.get(`${this.reflectionTimeApiUrl}/analytics${params}`);
  }

  getUserSessions(userId: string): Observable<AnnotationSession[]> {
    return this.http.get<AnnotationSession[]>(`${this.clickstreamApiUrl}/sessions?userId=${userId}`);
  }
}
