import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import {
  ClickstreamEvent,
  ReflectionTimeData,
  AnnotationSession,
  ClickstreamAnalytics
} from '../models/clickstream.models';
import { LocalStorageService } from './local-storage.service';

@Injectable({
  providedIn: 'root'
})
export class ClickstreamStorageService {
  private apiUrl = 'http://localhost:8000/clickstream';
  private reflectionApiUrl = 'http://localhost:8000/reflection-time';

  private httpOptions = {
    headers: new HttpHeaders({
      'Content-Type': 'application/json'
    })
  };

  // Local storage keys
  private readonly EVENTS_KEY = 'clickstream_events';
  private readonly REFLECTION_KEY = 'reflection_times';
  private readonly SESSION_KEY = 'annotation_session';

  constructor(
    private http: HttpClient,
    private localStorage: LocalStorageService
  ) {}

  // API Methods
  public sendClickstreamEvents(events: ClickstreamEvent[]): Observable<any> {
    return this.http.post(`${this.apiUrl}/events`, { events }, this.httpOptions);
  }

  public sendReflectionTimeData(reflectionData: ReflectionTimeData): Observable<any> {
    return this.http.post(`${this.reflectionApiUrl}/data`, reflectionData, this.httpOptions);
  }

  public sendSessionData(session: AnnotationSession): Observable<any> {
    return this.http.post(`${this.apiUrl}/session`, session, this.httpOptions);
  }

  public getReflectionAnalytics(userId: string, spectroName?: string): Observable<ClickstreamAnalytics> {
    const params = spectroName ? `?userId=${userId}&spectroName=${spectroName}` : `?userId=${userId}`;
    return this.http.get<ClickstreamAnalytics>(`${this.reflectionApiUrl}/analytics${params}`);
  }

  public getUserSessions(userId: string): Observable<AnnotationSession[]> {
    return this.http.get<AnnotationSession[]>(`${this.apiUrl}/sessions?userId=${userId}`);
  }

  // Local Storage Methods
  public saveEventsLocally(events: ClickstreamEvent[]): void {
    const existingEvents = this.getLocalEvents();
    const allEvents = [...existingEvents, ...events];
    this.localStorage.setItem(this.EVENTS_KEY, JSON.stringify(allEvents));
  }

  public getLocalEvents(): ClickstreamEvent[] {
    const eventsJson = this.localStorage.getItem(this.EVENTS_KEY);
    return eventsJson ? JSON.parse(eventsJson) : [];
  }

  public saveReflectionTimeLocally(reflectionData: ReflectionTimeData): void {
    const existingData = this.getLocalReflectionTimes();
    existingData.push(reflectionData);
    this.localStorage.setItem(this.REFLECTION_KEY, JSON.stringify(existingData));
  }

  public getLocalReflectionTimes(): ReflectionTimeData[] {
    const dataJson = this.localStorage.getItem(this.REFLECTION_KEY);
    return dataJson ? JSON.parse(dataJson) : [];
  }

  public saveSessionLocally(session: AnnotationSession): void {
    this.localStorage.setItem(this.SESSION_KEY, JSON.stringify(session));
  }

  public getLocalSession(): AnnotationSession | null {
    const sessionJson = this.localStorage.getItem(this.SESSION_KEY);
    return sessionJson ? JSON.parse(sessionJson) : null;
  }

  // Sync Methods
  public syncLocalDataToServer(): Observable<any> {
    return new Observable(observer => {
      const events = this.getLocalEvents();
      const reflectionTimes = this.getLocalReflectionTimes();
      const session = this.getLocalSession();

      let completedRequests = 0;
      const totalRequests = (events.length > 0 ? 1 : 0) +
        reflectionTimes.length +
        (session ? 1 : 0);

      if (totalRequests === 0) {
        observer.next({ message: 'No data to sync' });
        observer.complete();
        return;
      }

      const checkCompletion = () => {
        completedRequests++;
        if (completedRequests === totalRequests) {
          observer.next({ message: 'Sync completed successfully' });
          observer.complete();
        }
      };

      // Sync events
      if (events.length > 0) {
        this.sendClickstreamEvents(events).subscribe({
          next: () => {
            this.clearLocalEvents();
            checkCompletion();
          },
          error: (error) => {
            console.error('Failed to sync events:', error);
            checkCompletion();
          }
        });
      }

      // Sync reflection times
      reflectionTimes.forEach(reflectionData => {
        this.sendReflectionTimeData(reflectionData).subscribe({
          next: () => {
            checkCompletion();
          },
          error: (error) => {
            console.error('Failed to sync reflection data:', error);
            checkCompletion();
          }
        });
      });

      // Sync session
      if (session) {
        this.sendSessionData(session).subscribe({
          next: () => {
            checkCompletion();
          },
          error: (error) => {
            console.error('Failed to sync session:', error);
            checkCompletion();
          }
        });
      }
    });
  }

  // Analytics Methods
  public calculateLocalAnalytics(userId: string, spectroName?: string): ClickstreamAnalytics {
    const reflectionTimes = this.getLocalReflectionTimes()
      .filter(rt => rt.userId === userId && (!spectroName || rt.spectroName === spectroName));

    if (reflectionTimes.length === 0) {
      return this.getEmptyAnalytics(userId, spectroName || 'all');
    }

    const times = reflectionTimes.map(rt => rt.totalReflectionTime);
    const bruiteurTimes = reflectionTimes.map(rt => rt.bruiteurFieldTime);
    const confidenceTimes = reflectionTimes.map(rt => rt.confidenceFieldTime);

    // Calculate statistics
    const averageReflectionTime = times.reduce((a, b) => a + b, 0) / times.length;
    const sortedTimes = [...times].sort((a, b) => a - b);
    const medianReflectionTime = sortedTimes[Math.floor(sortedTimes.length / 2)];
    const minReflectionTime = Math.min(...times);
    const maxReflectionTime = Math.max(...times);

    // Field-specific analytics
    const averageBruiteurTime = bruiteurTimes.reduce((a, b) => a + b, 0) / bruiteurTimes.length;
    const averageConfidenceTime = confidenceTimes.reduce((a, b) => a + b, 0) / confidenceTimes.length;

    // Interaction patterns
    const totalChanges = reflectionTimes.reduce((sum, rt) => sum + rt.bruiteurChanges + rt.confidenceChanges, 0);
    const averageChangesPerAnnotation = totalChanges / reflectionTimes.length;

    // Most common values
    const bruiteurCounts = reflectionTimes.reduce((acc, rt) => {
      acc[rt.finalBruiteur] = (acc[rt.finalBruiteur] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    const mostCommonBruiteur = Object.keys(bruiteurCounts).reduce((a, b) =>
      bruiteurCounts[a] > bruiteurCounts[b] ? a : b, '');

    const confidenceCounts = reflectionTimes.reduce((acc, rt) => {
      acc[rt.finalConfidence] = (acc[rt.finalConfidence] || 0) + 1;
      return acc;
    }, {} as Record<number, number>);
    const mostCommonConfidence = Number(Object.keys(confidenceCounts).reduce((a, b) =>
      confidenceCounts[Number(a)] > confidenceCounts[Number(b)] ? a : b, '0'));

    // Efficiency metrics
    const totalSessionTime = reflectionTimes.reduce((sum, rt) => sum + rt.totalReflectionTime, 0);
    const annotationsPerMinute = (reflectionTimes.length / (totalSessionTime / 60000));

    // Hesitation score (higher = more hesitant)
    const hesitationScore = averageChangesPerAnnotation * (averageReflectionTime / 1000);

    // Click analytics calculations
    const events = this.getLocalEvents().filter(e => e.userId === userId);
    const leftClicks = events.filter(e => e.eventType === 'mouse_click').length;
    const rightClicks = events.filter(e => e.eventType === 'mouse_right_click').length;
    const wheelClicks = events.filter(e => e.eventType === 'mouse_wheel_click').length;
    const totalClicks = leftClicks + rightClicks + wheelClicks;

    const clicksPerMinute = totalSessionTime > 0 ? (totalClicks / (totalSessionTime / 60000)) : 0;
    const clicksPerAnnotation = reflectionTimes.length > 0 ? (totalClicks / reflectionTimes.length) : 0;

    //Ai analytics calculations
    const aiHelperActivations = events.filter(e => e.eventType === 'ai_helper_activated').length;
    const aiRectangleClicks = events.filter(e => e.eventType === 'ai_rectangle_clicked').length;
    const aiSuggestionsAccepted = events.filter(e => e.eventType === 'ai_suggestion_accepted').length;
    const aiSuggestionsModified = events.filter(e => e.eventType === 'ai_suggestion_modified').length;
    const aiSuggestionsRejected = events.filter(e => e.eventType === 'ai_suggestion_rejected').length;

    const totalAIInteractions = aiSuggestionsAccepted + aiSuggestionsModified + aiSuggestionsRejected;
    const aiAcceptanceRate = totalAIInteractions > 0 ? (aiSuggestionsAccepted / totalAIInteractions) * 100 : 0;
    const aiModificationRate = totalAIInteractions > 0? (aiSuggestionsModified / totalAIInteractions) * 100 : 0;
    const aiRejectionRate = totalAIInteractions > 0 ? (aiSuggestionsRejected / totalAIInteractions) * 100 : 0;

    //Calculate Ai average relfection time from ai-related annotations
    const aiReflectionTimes = reflectionTimes.filter(rt => rt.rectangleData &&
      events.some(e => e.eventType === 'ai_rectangle_clicked' &&
        e.timestamp >= rt.formOpenedAt && e.timestamp <= rt.formClosedAt));

    const averageAIReflectionTime = aiReflectionTimes.length > 0 ?
      aiReflectionTimes.reduce((sum, rt) => sum+ rt.totalReflectionTime, 0) / aiReflectionTimes.length : 0;


    return {
      userId,
      spectroName: spectroName || 'all',
      totalAnnotations: reflectionTimes.length,
      averageReflectionTime,
      medianReflectionTime,
      minReflectionTime,
      maxReflectionTime,
      totalSessionTime,
      averageBruiteurTime,
      averageConfidenceTime,
      averageChangesPerAnnotation,
      mostCommonBruiteur,
      mostCommonConfidence,
      annotationsPerMinute,
      timeToFirstInteraction: 0, // Would need additional tracking
      hesitationScore,
      // Click tracking metrics
      totalClicks,
      totalLeftClicks: leftClicks,
      totalRightClicks: rightClicks,
      totalWheelClicks: wheelClicks,
      clicksPerMinute,
      clicksPerAnnotation,
      //Ai Metrics
      aiHelperUsageCount: aiHelperActivations,
      aiRectangleClickCount: aiRectangleClicks,
      aiSuggestionsAccepted,
      aiSuggestionsModified,
      aiSuggestionsRejected,
      averageAIReflectionTime,
      aiAcceptanceRate,
      aiModificationRate,
      aiRejectionRate,
      totalAIInteractions
    };
  }

  private getEmptyAnalytics(userId: string, spectroName: string): ClickstreamAnalytics {
    return {
      userId,
      spectroName,
      totalAnnotations: 0,
      averageReflectionTime: 0,
      medianReflectionTime: 0,
      minReflectionTime: 0,
      maxReflectionTime: 0,
      totalSessionTime: 0,
      averageBruiteurTime: 0,
      averageConfidenceTime: 0,
      averageChangesPerAnnotation: 0,
      mostCommonBruiteur: '',
      mostCommonConfidence: 0,
      annotationsPerMinute: 0,
      timeToFirstInteraction: 0,
      hesitationScore: 0,
      // Click tracking metrics
      totalClicks: 0,
      totalLeftClicks: 0,
      totalRightClicks: 0,
      totalWheelClicks: 0,
      clicksPerMinute: 0,
      clicksPerAnnotation: 0,
      //Ai metrics
      aiHelperUsageCount: 0,
      aiRectangleClickCount: 0,
      aiSuggestionsAccepted: 0,
      aiSuggestionsModified: 0,
      aiSuggestionsRejected: 0,
      averageAIReflectionTime: 0,
      aiAcceptanceRate: 0,
      aiModificationRate: 0,
      aiRejectionRate: 0,
      totalAIInteractions: 0
    };
  }

  // Cleanup Methods
  public clearLocalEvents(): void {
    this.localStorage.removeItem(this.EVENTS_KEY);
  }

  public clearLocalReflectionTimes(): void {
    this.localStorage.removeItem(this.REFLECTION_KEY);
  }

  public clearLocalSession(): void {
    this.localStorage.removeItem(this.SESSION_KEY);
  }

  public clearAllLocalData(): void {
    this.clearLocalEvents();
    this.clearLocalReflectionTimes();
    this.clearLocalSession();
  }
}
