import { Injectable } from '@angular/core';
import {Rectangle, RectangleHelper} from "../components/spectro/spectro.component";

@Injectable({
  providedIn: 'root'
})
export class RectangleService {
  private rectangles: Rectangle[] = [];
  private aiRectangles: Rectangle[] = [];

  getRectangle(){
    return this.rectangles;
  }

  addRectangle(rectangle: Rectangle){
    rectangle.isPersisted = false;
    this.rectangles.push(rectangle);
    console.log('je push sure?');
  }

  setRectangles(rectangles: Rectangle[]){
    this.rectangles = rectangles;
  }

  getAiRectangles() {
    return this.aiRectangles;
  }

  clearAiRectangles() {
    this.aiRectangles = [];
  }

  setAIRectangles(rectangles: Rectangle[]){
    this.aiRectangles = rectangles;
    console.log('setAIRectangles from rectangle serivce : ', this.getAiRectangles());
  }

  removeAiRectangle(rectangle: Rectangle){
    this.aiRectangles = this.aiRectangles.filter(r => r !== rectangle);
  }

  removeLastRectangle(){
    this.rectangles.pop();
  }

  removeRectangle(rect: Rectangle){
    const index = this.rectangles.indexOf(rect);
    console.log('je suis index : ', index);
    if (index > -1) {
      this.rectangles.splice(index, 1);
    }
  }

  updateRectangle(updatedRect: Rectangle){
    const index = this.rectangles.findIndex(r =>
    r.fLeft === updatedRect.fLeft &&
    r.fRight === updatedRect.fRight &&
    r.tTop === updatedRect.tTop &&
    r.tBottom === updatedRect.tBottom
    );

    if (index !== -1) {
      this.rectangles[index] = {...updatedRect};
    }
  }


  clearRectangle(){
    this.rectangles = [];
  }

  constructor() { }
}
