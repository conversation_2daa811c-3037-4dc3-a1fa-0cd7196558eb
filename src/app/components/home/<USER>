import { Component , OnInit} from '@angular/core';
import {Router, RouterLink} from "@angular/router";
import {UserServiceService} from "../../services/user-service.service";
import {generate, count} from "random-words";
import {LocalStorageService} from "../../services/local-storage.service";
import {IFinishTime} from "../../services/finish-time.service";


@Component({
  selector: 'app-home',
  standalone: true,
  imports: [RouterLink],
  templateUrl: './home.component.html',
  styleUrl: './home.component.css'
})
export class HomeComponent implements OnInit {
  uniqueId: string | null = null;

  constructor(private localStorageService: LocalStorageService) {}

  ngOnInit() {
    this.uniqueId = this.localStorageService.getItem("uniqueId")
    console.log(localStorage.getItem('uniqueId'))
  }
}

