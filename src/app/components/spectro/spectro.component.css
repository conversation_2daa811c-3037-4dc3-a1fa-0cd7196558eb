html, body {
  margin: 0;
  padding: 0;
  overflow: hidden;
  height: 100%;
}

.image-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

canvas {
  display: block;
  max-width: 100%;
  max-height: 100%;
  cursor: auto;
}

.cursor-info {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 10px;
  border-radius: 3px;
  pointer-events: none;
  font-size: 18px;
  z-index: 10;
}

.page-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

app-timer.timer-container {
  font-size: 50px;
  font-weight: bold;
  text-align: right;
  padding: 10px 20px;
  color: black;
  background-color: #f8f9fa;
  border-bottom: 2px solid #ddd;
  position: sticky;
  top: 0;
  z-index: 10;
}

app-topbar.navbar {
  list-style-type: none;
  padding: 0;
  margin: 0;
  display: flex;
  gap: 20px;
}

/* Add these styles to your existing CSS file */

.middle-mouse-dot {
  position: absolute;
  width: 10px;
  height: 10px;
  background-color: orange;
  border-radius: 50%;
  z-index: 1000;
  pointer-events: none;
}

.middle-mouse-line {
  position: absolute;
  width: 2px;
  background-color: orange;
  z-index: 1000;
  pointer-events: none;
}

.disabled {
  pointer-events: none;
}

#rect-info {
  position: absolute;
  display: none;
  background: white;
  border: 1px solid black;
  padding: 10px;
  z-index: 10000;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  border-radius: 3px;
  font-size: 14px;
  color: #333;
}

#rect-info p {
  margin: 0 0 5px;
}

.click-dot {
  position: fixed;
  width: 10px;
  height: 10px;
  background-color: white;
  border-radius: 50%;
  pointer-events: none;
}
