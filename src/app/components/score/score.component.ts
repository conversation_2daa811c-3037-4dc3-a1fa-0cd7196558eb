import {AfterViewInit, Component, ElementRef, OnInit} from '@angular/core';
import {LocalStorageService} from "../../services/local-storage.service";
import {Router} from "@angular/router";
import {StatScoreService} from "../../services/stat-score.service";
import {NgForOf} from "@angular/common";
import {Chart, BarController, BarElement, CategoryScale, LinearScale, Title, Tooltip} from "chart.js";

@Component({
  selector: 'app-score',
  standalone: true,
  imports: [
    NgForOf
  ],
  templateUrl: './score.component.html',
  styleUrl: './score.component.css'
})
export class ScoreComponent implements AfterViewInit {
  userName;
  spectroName;
  noiseData: any[] = [];
  chart: any;

  constructor(private elementRef: ElementRef, private localService: LocalStorageService, private router: Router,
              private statScoreService: StatScoreService) {

    this.userName = this.localService.getItem("uniqueId");
    this.spectroName = this.localService.getItem("spectroName");
  }

  ngAfterViewInit() {
    this.elementRef.nativeElement.ownerDocument.body.style.backgroundColor = 'black';
    this.getNoiseData();
  }

  goToHome(): void {
    this.router.navigate(['/']);
    this.localService.clearItem();
    this.elementRef.nativeElement.ownerDocument.body.style.backgroundColor = 'white';
  }

  getNoiseData(): void {
    this.statScoreService.getNoiseData(this.userName!, this.spectroName!).subscribe(response => {
      this.noiseData = response;
      this.renderChart();
    },
      error => {
      console.error(error);
      }
    );
  }

  renderChart(){
    Chart.register(BarController, BarElement, CategoryScale, LinearScale, Title, Tooltip);
    const ctx = document.getElementById('noiseChart') as HTMLCanvasElement;

    if (this.chart){
      this.chart.destroy();
    }

    this.chart = new Chart(ctx, {
      type: 'bar',
      data: {
        labels: this.noiseData.map(item => item.type_bruiteur),
        datasets: [{
          label: 'Nombre de bruiteur',
          data: this.noiseData.map(item => item.number_total),
          backgroundColor: 'rgba(0, 191, 255, 0.6)',
          borderColor: 'rgba(0, 191, 255, 1)',
          borderWidth: 1,
        }]
      }, options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            labels: {
              font: {
                size: 20
              },
              color: '#fff',
            }
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              font: {
                size: 20
              },
              color: '#fff'},
            grid: { color: 'rgba(255, 255, 255, 0.2)'}
          },
          x: {
            beginAtZero: true,
            ticks: {
              font : {
                size: 20
              },
              color: '#fff'},
            grid: { color: 'rgba(255, 255, 255, 0.2)'}
          }
        }
      }
    })
  }
}
