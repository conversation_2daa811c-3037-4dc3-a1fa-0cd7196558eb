html, body {
  margin: 0;
  padding: 0;
  overflow: hidden;
  height: 100%;
}

.image-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

canvas {
  display: block;
  max-width: 100%;
  max-height: 100%;
  border: 1px solid #ccc;
  cursor: crosshair;
}

.page-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

app-timer.timer-container {
  font-size: 50px;
  font-weight: bold;
  text-align: right;
  padding: 10px 20px;
  color: black;
  background-color: #f8f9fa;
  border-bottom: 2px solid #ddd;
  position: sticky;
  top: 0;
  z-index: 10;
}

app-topbar.navbar {
  list-style-type: none;
  padding: 0;
  margin: 0;
  display: flex;
  gap: 20px;
}

button {
  display: flex;
  align-items: center;
  font-family: inherit;
  cursor: pointer;
  font-weight: 500;
  font-size: 17px;
  padding: 0.8em 1.3em 0.8em 0.9em;
  color: white;
  background: #ad5389;
  background: linear-gradient(to right, #0f0c29, #302b63, #24243e);
  border: none;
  letter-spacing: 0.05em;
  border-radius: 16px;
}

button svg {
  margin-right: 3px;
  transform: rotate(30deg);
  transition: transform 0.5s cubic-bezier(0.76, 0, 0.24, 1);
}

button span {
  transition: transform 0.5s cubic-bezier(0.76, 0, 0.24, 1);
}

button:hover svg {
  transform: translateX(5px) rotate(90deg);
}

button:hover span {
  transform: translateX(7px);
}

body {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f5f5f5;
}

.button {
  position: relative;
  width: 192px;
  height: 56px;
  background-color: white;
  border-radius: 16px;
  font-size: 18px;
  font-weight: 600;
  color: black;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  overflow: hidden;
  border: none;
  outline: none;
}

.button:hover .icon-container {
  width: 184px;
}

.icon-container {
  position: absolute;
  left: 4px;
  top: 4px;
  width: 48px;
  height: 48px;
  background-color: #4caf50;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: width 0.5s ease;
  z-index: 10;
}

.icon-container svg {
  width: 25px;
  height: 25px;
  fill: black;
}

.button p {
  margin-left: 10px;
  z-index: 5;
}

