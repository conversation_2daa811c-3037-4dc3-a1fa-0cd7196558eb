<div class="container">
  <h1>Bienvenue</h1>

  <div class="button-group">
    <button (click)="showCreateLogin()">Créer un identifiant</button>
    <button (click)="showLogin()">Se connecter</button>
  </div>

  <!-- Create Login Form -->
  <div *ngIf="creatingLogin" class="form">
    <h3>Identifiant</h3>
    <input type="text" [(ngModel)]="newLogin" placeholder="Entrez un mot facile à retenir"/>
    <button (click)="saveLogin()">Enregistrer</button>
    <p *ngIf="message" class="message">{{ message }}</p>
  </div>

  <!-- Login Form -->
  <div *ngIf="loggingIn" class="form">
    <h3>Login</h3>
    <input type="text" [(ngModel)]="enteredLogin" placeholder="Entrez votre identifiant" />
    <button (click)="connect()">Se connecter</button>
    <p *ngIf="message" class="message">{{ message }}</p>
  </div>
</div>
