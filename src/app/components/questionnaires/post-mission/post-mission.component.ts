import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { NgIf, NgForOf } from '@angular/common';
import { Router } from '@angular/router';
import { LocalStorageService } from '../../../services/local-storage.service';
import { ClickstreamService } from '../../../services/clickstream.service';
import { ClickstreamEventType } from '../../../models/clickstream.models';

interface MentalLoadQuestion {
  id: number;
  text: string;
}

interface FatigueQuestion {
  id: number;
  text: string;
}

interface SatisfactionQuestion {
  id: number;
  text: string;
}

@Component({
  selector: 'app-post-mission',
  imports: [ReactiveFormsModule, NgIf, NgForOf],
  templateUrl: './post-mission.component.html',
  styleUrl: './post-mission.component.css'
})
export class PostMissionComponent implements OnInit {
  currentPage: number = 0;
  totalPages: number = 4;

  questionnaireForms: FormGroup[] = [];

  // Reflection time tracking
  private pageStartTime: number = 0;
  private questionnaireStartTime: number = 0;
  private pageReflectionTimes: number[] = [];

  // Mental Load Questions (7-point scale)
  mentalLoadQuestions: MentalLoadQuestion[] = [
    { id: 1, text: "Dans quelle mesure la tâche était-elle exigeante sur le plan mental ?" },
    { id: 2, text: "Quels efforts avez-vous dû fournir (mentalement) pour atteindre votre niveau de performance ?" },
    { id: 3, text: "Dans quelle mesure avez-vous trouvé la tâche complexe ?" },
    { id: 4, text: "Dans quelle mesure pensez-vous avoir atteint vos objectifs ?" },
    { id: 5, text: "Avez-vous ressenti du stress, de l'irritabilité ou de la frustration pendant l'expérience ?" },
    { id: 6, text: "Je me sens mentalement vide après cette tâche" }
  ];

  // Fatigue Questions (5-point scale)
  fatigueQuestions: FatigueQuestion[] = [
    { id: 1, text: "Je me sens physiquement épuisé après avoir accompli cette tâche." },
    { id: 2, text: "J'ai eu du mal à rester concentré pendant la tâche" },
    { id: 3, text: "J'ai l'impression d'avoir moins d'énergie que d'habitude à ce stade." },
    { id: 4, text: "Je me sens moins motivé pour continuer à travailler sur des tâches similaires." },
    { id: 5, text: "Je pense que j'aurai besoin d'un repos ou d'une pause notable pour me remettre de cette tâche." }
  ];

  // Self-Satisfaction Questions (5-point scale)
  satisfactionQuestions: SatisfactionQuestion[] = [
    { id: 1, text: "Je suis satisfait de la manière dont j'ai réalisé la tâche" },
    { id: 2, text: "Je pense avoir bien accompli la mission" },
    { id: 3, text: "Je pense avoir obtenu un bon score" }
  ];

  // Scale options
  mentalLoadOptions = [
    { value: 1, label: '1' },
    { value: 2, label: '2' },
    { value: 3, label: '3' },
    { value: 4, label: '4' },
    { value: 5, label: '5' },
    { value: 6, label: '6' },
    { value: 7, label: '7' }
  ];

  fatigueOptions = [
    { value: 1, label: 'Fortement en désaccord' },
    { value: 2, label: 'En désaccord' },
    { value: 3, label: 'Ni d\'accord ni en désaccord' },
    { value: 4, label: 'D\'accord' },
    { value: 5, label: 'Tout à fait d\'accord' }
  ];

  satisfactionOptions = [
    { value: 1, label: 'Fortement en désaccord' },
    { value: 2, label: 'En désaccord' },
    { value: 3, label: 'Ni d\'accord ni en désaccord' },
    { value: 4, label: 'D\'accord' },
    { value: 5, label: 'Tout à fait d\'accord' }
  ];

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private localStorage: LocalStorageService,
    private clickstreamService: ClickstreamService
  ) {}

  ngOnInit() {
    this.initializeForms();
    this.startReflectionTracking();

    // Track questionnaire start
    this.clickstreamService.trackEvent(ClickstreamEventType.QUESTIONNAIRE_STARTED, {
      elementId: 'post-mission-questionnaire',
      metadata: {
        userId: this.localStorage.getItem('uniqueId'),
        timestamp: new Date().toISOString(),
        totalPages: this.totalPages
      }
    });
  }

  initializeForms() {
    // Welcome page form (no questions)
    this.questionnaireForms[0] = this.fb.group({});

    // Mental Load form
    const mentalLoadControls = this.mentalLoadQuestions.reduce((group, q) => {
      group['q' + q.id] = [null, Validators.required];
      return group;
    }, {} as any);
    this.questionnaireForms[1] = this.fb.group(mentalLoadControls);

    // Fatigue form
    const fatigueControls = this.fatigueQuestions.reduce((group, q) => {
      group['q' + q.id] = [null, Validators.required];
      return group;
    }, {} as any);
    this.questionnaireForms[2] = this.fb.group(fatigueControls);

    // Satisfaction form
    const satisfactionControls = this.satisfactionQuestions.reduce((group, q) => {
      group['q' + q.id] = [null, Validators.required];
      return group;
    }, {} as any);
    this.questionnaireForms[3] = this.fb.group(satisfactionControls);
  }

  nextPage() {
    if (this.currentPage < this.totalPages - 1) {
      if (this.currentPage === 0 || this.isCurrentPageValid()) {
        // Record time spent on current page
        this.recordPageReflectionTime();

        // Track page navigation
        this.clickstreamService.trackEvent(ClickstreamEventType.QUESTIONNAIRE_PAGE_NEXT, {
          elementId: 'next-button',
          metadata: {
            fromPage: this.currentPage,
            toPage: this.currentPage + 1,
            pageTitle: this.getPageTitle(),
            timeSpentOnPage: this.getPageReflectionTime(),
            timestamp: Date.now()
          }
        });

        this.currentPage++;
        this.startPageReflectionTracking();
      }
    }
  }

  previousPage() {
    if (this.currentPage > 0) {
      // Record time spent on current page
      this.recordPageReflectionTime();

      // Track page navigation
      this.clickstreamService.trackEvent(ClickstreamEventType.QUESTIONNAIRE_PAGE_PREVIOUS, {
        elementId: 'previous-button',
        metadata: {
          fromPage: this.currentPage,
          toPage: this.currentPage - 1,
          pageTitle: this.getPageTitle(),
          timeSpentOnPage: this.getPageReflectionTime(),
          timestamp: Date.now()
        }
      });

      this.currentPage--;
      this.startPageReflectionTracking();
    }
  }

  isCurrentPageValid(): boolean {
    if (this.currentPage === 0) return true; // Welcome page
    return this.questionnaireForms[this.currentPage].valid;
  }

  canProceed(): boolean {
    return this.currentPage === 0 || this.isCurrentPageValid();
  }

  finishQuestionnaire() {
    if (this.areAllFormsValid()) {
      // Record final page reflection time
      this.recordPageReflectionTime();

      const totalReflectionTime = Date.now() - this.questionnaireStartTime;

      // Track questionnaire completion
      this.clickstreamService.trackEvent(ClickstreamEventType.QUESTIONNAIRE_COMPLETED, {
        elementId: 'finish-button',
        metadata: {
          userId: this.localStorage.getItem('uniqueId'),
          completionTime: new Date().toISOString(),
          totalPages: this.totalPages,
          totalReflectionTime: totalReflectionTime,
          pageReflectionTimes: this.pageReflectionTimes,
          timestamp: Date.now(),
          questionnaireData: this.getQuestionnaireResponses()
        }
      });

      this.saveQuestionnaireData();
      // Navigate to next component or show completion message
      this.router.navigate(['/score']); // Adjust route as needed
    }
  }

  areAllFormsValid(): boolean {
    return this.questionnaireForms.slice(1).every(form => form.valid);
  }

  saveQuestionnaireData() {
    const totalReflectionTime = Date.now() - this.questionnaireStartTime;

    const questionnaireData = {
      timestamp: new Date().toISOString(),
      userId: this.localStorage.getItem('uniqueId'),
      sessionId: this.clickstreamService.exportData().session?.sessionId || 'unknown',
      mentalLoad: this.questionnaireForms[1].value,
      fatigue: this.questionnaireForms[2].value,
      satisfaction: this.questionnaireForms[3].value,
      reflectionTimeData: {
        totalReflectionTime: totalReflectionTime,
        pageReflectionTimes: this.pageReflectionTimes,
        averagePageTime: this.pageReflectionTimes.reduce((a, b) => a + b, 0) / this.pageReflectionTimes.length
      }
    };

    // Save to localStorage
    this.localStorage.setItem('post_mission_questionnaire', JSON.stringify(questionnaireData));

    // TODO: Send to backend API if needed
    console.log('Questionnaire data saved:', questionnaireData);
  }

  getPageTitle(): string {
    switch (this.currentPage) {
      case 0: return 'Questionnaire';
      case 1: return 'Charge mentale';
      case 2: return 'Fatigue';
      case 3: return 'Satisfaction de soi';
      default: return 'Questionnaire';
    }
  }

  getQuestionnaireResponses() {
    return {
      mentalLoad: this.questionnaireForms[1]?.value || {},
      fatigue: this.questionnaireForms[2]?.value || {},
      satisfaction: this.questionnaireForms[3]?.value || {}
    };
  }

  trackQuestionResponse(questionId: string, value: any, questionType: 'mental_load' | 'fatigue' | 'satisfaction') {
    this.clickstreamService.trackEvent(ClickstreamEventType.QUESTIONNAIRE_QUESTION_ANSWERED, {
      elementId: `question-${questionId}`,
      elementType: 'radio-button',
      value: value,
      metadata: {
        questionId,
        questionType,
        currentPage: this.currentPage,
        pageTitle: this.getPageTitle(),
        timestamp: Date.now()
      }
    });
  }

  // Reflection time tracking methods
  private startReflectionTracking() {
    this.questionnaireStartTime = Date.now();
    this.startPageReflectionTracking();
  }

  private startPageReflectionTracking() {
    this.pageStartTime = Date.now();
  }

  private getPageReflectionTime(): number {
    return Date.now() - this.pageStartTime;
  }

  private recordPageReflectionTime() {
    const reflectionTime = this.getPageReflectionTime();
    this.pageReflectionTimes[this.currentPage] = reflectionTime;

    // Store reflection time data for this page
    const reflectionData = {
      userId: this.localStorage.getItem('uniqueId') || 'anonymous',
      sessionId: this.clickstreamService.exportData().session?.sessionId || 'unknown',
      pageNumber: this.currentPage,
      pageTitle: this.getPageTitle(),
      reflectionTime: reflectionTime,
      timestamp: Date.now()
    };

    // Save to localStorage for potential backend submission
    const existingReflectionTimes = JSON.parse(this.localStorage.getItem('questionnaire_reflection_times') || '[]');
    existingReflectionTimes.push(reflectionData);
    this.localStorage.setItem('questionnaire_reflection_times', JSON.stringify(existingReflectionTimes));
  }
}
