.questionnaire-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
  background-color: #f9f9f9;
  min-height: 100vh;
}

.progress-indicator {
  text-align: center;
  margin-bottom: 20px;
  font-weight: bold;
  color: #666;
}

.page {
  background-color: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  min-height: 500px;
}

.welcome-page h1 {
  color: #4a90e2;
  text-align: center;
  margin-bottom: 30px;
  font-size: 2.2em;
}

.welcome-content p {
  line-height: 1.6;
  margin-bottom: 20px;
  text-align: justify;
  font-size: 1.1em;
  color: #333;
}

.questionnaire-page h2 {
  color: #4a90e2;
  margin-bottom: 20px;
  font-size: 1.8em;
}

.instructions {
  background-color: #f0f8ff;
  padding: 15px;
  border-left: 4px solid #4a90e2;
  margin-bottom: 25px;
  font-style: italic;
  line-height: 1.5;
}

.questionnaire-form {
  margin-top: 20px;
}

.scale-header {
  display: flex;
  margin-bottom: 15px;
  border-bottom: 2px solid #ddd;
  padding-bottom: 10px;
}

.question-column {
  flex: 1;
  font-weight: bold;
}

.scale-options {
  display: flex;
  justify-content: space-between;
  width: 400px;
  align-items: center;
}

.scale-options.fatigue-scale,
.scale-options.satisfaction-scale {
  width: 500px;
}

.scale-label {
  font-size: 0.9em;
  font-weight: bold;
  color: #666;
  text-align: center;
  flex: 1;
}

.question-row {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 5px;
  background-color: #fafafa;
}

.question-row:hover {
  background-color: #f0f0f0;
}

.question-text {
  flex: 1;
  font-weight: 500;
  line-height: 1.4;
  margin-right: 20px;
}

.radio-options {
  display: flex;
  justify-content: space-between;
  width: 400px;
}

.questionnaire-page:nth-child(3) .radio-options,
.questionnaire-page:nth-child(4) .radio-options {
  width: 500px;
}

.radio-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  flex: 1;
}

.radio-option input[type="radio"] {
  display: none;
}

.radio-circle {
  width: 20px;
  height: 20px;
  border: 2px solid #ccc;
  border-radius: 50%;
  display: inline-block;
  position: relative;
  transition: all 0.3s ease;
}

.radio-option input[type="radio"]:checked + .radio-circle {
  border-color: #4a90e2;
  background-color: #4a90e2;
}

.radio-option input[type="radio"]:checked + .radio-circle::after {
  content: '';
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: white;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.radio-option:hover .radio-circle {
  border-color: #4a90e2;
  transform: scale(1.1);
}

.navigation-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 30px;
  padding: 0 30px;
}

.nav-button {
  padding: 12px 24px;
  border: none;
  border-radius: 5px;
  font-size: 1.1em;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
}

.prev-button {
  background-color: #6c757d;
  color: white;
}

.prev-button:hover {
  background-color: #5a6268;
}

.next-button {
  background-color: #4a90e2;
  color: white;
}

.next-button:hover:not(:disabled) {
  background-color: #357abd;
}

.finish-button {
  background-color: #28a745;
  color: white;
}

.finish-button:hover:not(:disabled) {
  background-color: #218838;
}

.nav-button:disabled {
  background-color: #ccc;
  color: #666;
  cursor: not-allowed;
  opacity: 0.6;
}

.validation-message {
  background-color: #fff3cd;
  color: #856404;
  padding: 10px 15px;
  border: 1px solid #ffeaa7;
  border-radius: 5px;
  margin-top: 15px;
  text-align: center;
  font-weight: bold;
}

/* Responsive design */
@media (max-width: 768px) {
  .questionnaire-container {
    padding: 10px;
  }

  .page {
    padding: 20px;
  }

  .question-row {
    flex-direction: column;
    align-items: flex-start;
  }

  .question-text {
    margin-bottom: 15px;
    margin-right: 0;
  }

  .radio-options {
    width: 100%;
    justify-content: space-around;
  }

  .scale-options {
    width: 100%;
    flex-wrap: wrap;
  }

  .scale-label {
    font-size: 0.8em;
    margin-bottom: 5px;
  }

  .navigation-buttons {
    flex-direction: column;
    gap: 10px;
  }

  .nav-button {
    width: 100%;
  }
}
