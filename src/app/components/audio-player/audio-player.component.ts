import {AfterViewInit, Component, ElementRef, EventEmitter, Input, OnChanges, Output, ViewChild} from '@angular/core';
import WaveSurfer from 'wavesurfer.js';
import {NgStyle} from '@angular/common';
import {AudioService} from "../../services/audio.service";
import {LocalStorageService} from "../../services/local-storage.service";


@Component({
  selector: 'app-audio-player',
  imports: [
    NgStyle
  ],
  templateUrl: './audio-player.component.html',
  standalone: true,
  styleUrl: './audio-player.component.css'
})
export class AudioPlayerComponent implements OnChanges, AfterViewInit{
  @ViewChild('waveform') waveformRef!: ElementRef;
  @ViewChild('playBtn') playBtnRef!: ElementRef;

  @Input() startTime!: number;
  @Input() endTime!: number;
  @Output() close = new EventEmitter<void>();

  wavesurfer!: WaveSurfer;
  isPlaying = false;
  remaingTime:number = 0
  totalTime:number = 0;


  constructor(private audioService: AudioService, private localstorageService: LocalStorageService) {}

  ngAfterViewInit() {
    this.wavesurfer = WaveSurfer.create({
      container: this.waveformRef.nativeElement,
      waveColor: 'rgb(200, 0, 200)',
      progressColor: 'rgb(100, 0, 100)',
      height: 50,
      width: 550
    });
  }

  ngOnChanges(): void {
    if (this.startTime !== undefined && this.endTime !== undefined) {
      this.playAudioSegment();
    }
  }

  playAudioSegment(){
    const totalDuration = (this.endTime - this.startTime) / 1000
    const nameImage = this.localstorageService.getItem('spectroName');

    this.remaingTime = totalDuration;
    this.totalTime = totalDuration;

    this.audioService.getSplitAudio(nameImage!,this.startTime, this.endTime).subscribe(url => {

      if (this.wavesurfer) {
        // this.wavesurfer.empty();
        this.isPlaying = true;
        this.wavesurfer.load(url!);
        this.wavesurfer.on('ready', () => this.wavesurfer.play());

        this.wavesurfer.on('audioprocess', () => {
          const currentTime = this.wavesurfer.getCurrentTime();
          this.remaingTime = Math.max(0, totalDuration - currentTime);

        });

        this.wavesurfer.on('finish', () => {
          this.remaingTime = 0;
          this.totalTime = 0;
        })
      }
    });
  }

  // Toggle between play and pause
  togglePlay() {
    if (this.isPlaying) {
      this.wavesurfer.pause();
      this.isPlaying = false;
    } else {
      this.wavesurfer.play();
      this.isPlaying = true;
    }
  }

  closePlayer() {
    this.close.emit();
  }

  convertTimeToSeconds(ms: number) {
    const minutes = Math.floor(ms / 60000);
    const secs = ((minutes % 60000) / 1000).toFixed(1)

    const [wholeSec, decimal] = secs.split('.');
    const paddedSec = wholeSec.padStart(2, '0');
    return `${minutes}:${paddedSec}.${decimal}`;
  }

}
