import {Component, Input, AfterViewInit, OnChanges, SimpleChanges, ViewChild, ElementRef} from '@angular/core';
import {TopbarComponent} from "../topbar/topbar.component";
import {CustomCursorComponent} from "../custom-cursor/custom-cursor.component";
import {PinchZoomModule} from "@meddv/ngx-pinch-zoom";
import {TimerComponent} from "../timer/timer.component";

@Component({
  selector: 'app-test',
  standalone: true,
  imports: [TopbarComponent, PinchZoomModule, TimerComponent],
  templateUrl: './test.component.html',
  styleUrl: './test.component.css'
})
export class TestComponent implements AfterViewInit {
  constructor(private elementRef: ElementRef) {}

  ngAfterViewInit() {
    this.elementRef.nativeElement.ownerDocument.body.style.backgroundColor = 'black';
  }

  @ViewChild('imageCanvas', { static: true }) canvasRef!: ElementRef<HTMLCanvasElement>;

  cursorX: number = 0;
  cursorY: number = 0;

  frequency: number = 0;
  time : number = 0;

  Maxfrequency = 22050;
  maxTime = 3600000;

  private canvas!: HTMLCanvasElement;
  private ctx!: CanvasRenderingContext2D;
  private image!: HTMLImageElement;

  ngOnInit(): void {
    this.initializeCanvas();
  }

  initializeCanvas(): void {
    this.canvas = this.canvasRef.nativeElement
    this.ctx = this.canvas.getContext('2d')!;

    this.image = new Image();
    this.image.src = 'img/spectro_complet.png';
    this.image.onload = () => {
      this.canvas.width = this.image.width;
      this.canvas.height = this.image.height;

      this.ctx.drawImage(this.image, 0, 0, this.canvas.width, this.canvas.height);
    };
  }

  onMouseMove(event: MouseEvent): void {
    const rect = this.canvas.getBoundingClientRect();

    this.cursorX = Math.floor(event.clientX - rect.left);
    this.cursorY = Math.floor(event.clientY - rect.top);

    if (this.cursorX >= 0 && this.cursorX  <= this.canvas.width && this.cursorY >= 0 && this.cursorY <= this.canvas.height) {
      this.frequency = (this.Maxfrequency * this.cursorX) / this.canvas.width;
      this.time = this.maxTime * (1 - this.cursorY / this.canvas.height);

      console.clear()
      console.log(`Cursor position X = ${this.cursorX} Y = ${this.cursorY}`);
      console.log(`Frequency = ${this.frequency.toFixed(2)} Hz`);
      console.log(`Time = ${(this.time / 60000).toFixed(2)} m`);
    } else {
      this.resetCursorPosition();
    }
  }

  resetCursorPosition(): void {
    this.cursorX = 0;
    this.cursorY = 0;
    this.frequency = 0;
    this.time = 0;
  }

}
