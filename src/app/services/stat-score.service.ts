import { Injectable } from '@angular/core';
import {HttpClient} from "@angular/common/http";
import {Observable} from "rxjs";

@Injectable({
  providedIn: 'root'
})
export class StatScoreService {
  private baseUrl = 'http://localhost:8000/stats/count_annotation';

  constructor(private http: HttpClient) { }

  getNoiseData(name_user: string, name_image: string): Observable<any>{
    return this.http.get<any>(`${this.baseUrl}?image_name=${name_image}&user_name=${name_user}`);
  }
}
