import { Injectable } from '@angular/core';
import {HttpClient} from "@angular/common/http";
import {Observable} from "rxjs";

export interface IUser {
  unique_identifier: string;
}

@Injectable({
  providedIn: 'root'
})
export class UserServiceService {
  private userData: any = {};
  private apiUrl = 'http://localhost:8000';

  constructor(private http: HttpClient) {}

  setUserData(data: any) {
    this.userData = data;
  }

  getUserData() {
    return this.userData;
  }

  createUser(item: string): Observable<IUser> {
    return this.http.post<IUser>(`${this.apiUrl}/create_users/?unique_id=${item}`, {});
  }

  connect(identity: string): Observable<IUser> {
    return this.http.post<IUser>(`${this.apiUrl}/connect?identifier=${identity}`, {});
  }

}
