p, li  {
  font-size: 18px;
  line-height: 2;
  font-family: "Times New Roman", Times, serif;
  color: white;
  text-align: center;
}

hr {
  color: white;
  width: 50%;
  margin-top: 25px;
  margin-bottom: 25px;
}

h2 {
  color: red;
  text-align: center;
  font-size: 22px;
}

h1 {
  text-align: center;
  color: white;
}

h3 {
  text-align: center;
  color: white;
  font-size: 20px;
}

ul {
  text-align: center;
  list-style-position: inside;
  padding: 0;
}

img {
  margin: auto;
  display: block;
  width: 50%;
}

.button {
  position: relative;
  width: 215px;
  height: 56px;
  background-color: white;
  border-radius: 16px;
  font-size: 18px;
  font-weight: 600;
  color: black;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  overflow: hidden;
  border: none;
  outline: none;
  margin: auto;
}

.button:hover .icon-container {
  width: 200px;
}

.icon-container {
  position: absolute;
  left: 4px;
  top: 4px;
  width: 48px;
  height: 48px;
  background-color: #4caf50;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: width 0.5s ease;
  z-index: 10;
}

.icon-container svg {
  width: 25px;
  height: 25px;
  fill: black;
}

.button p {
  margin-left: 32px;
  z-index: 5;
}

.back {
  color: #0f0c29;
}

.button-back {
  text-align: center;
}

.icon-bruiteur {
  width: 24px;
}
