<div *ngIf="isAnnotationCreatorVisibile" class="annotation-creator" cdkDrag cdkDragBoundary=".container">
  <div class="header">Annotation Creator</div>
  <button type="button" class="close-btn" (click)="OnClose()">X</button>
  <form>
    <div class="form-group">
      <label for="annotationBruiteur">Type de bruiteur :</label>
      <select
        id="annotationBruiteur"
        name="bruiteur"
        [(ngModel)]="rectangleData.bruiteur"
        (focus)="onBruiteurFocus()"
        (blur)="onBruiteurBlur()"
        (change)="onBruiteurChange($event)"
        required>
        <option>Brouilleur</option>
        <option>Hélice</option>
        <option>SonarCW</option>
        <option>SonarFM1</option>
        <option>SonarFM2</option>
        <option>Rejet</option>
      </select>
    </div>

    <div class="form-group">
      <label for="annotationConfiance">Pourcentage confiance :</label>
      <select
        id="annotationConfiance"
        name="trust"
        [(ngModel)]="rectangleData.trust"
        (focus)="onConfidenceFocus()"
        (blur)="onConfidenceBlur()"
        (change)="onConfidenceChange($event)"
        required>
        <option>0</option>
        <option>25</option>
        <option>50</option>
        <option>75</option>
        <option>100</option>
      </select>
    </div>


    <div class="resize-handle bottom-right" (mousedown)="startResizing($event, 'bottom-right')"></div>
    <button type="button" (click)="onSave()">Confirmer</button>
    <button type="button" class="delete-annotation" (click)="onDelete()">Supprimer Annotation</button>
  </form>
</div>
