import { Injectable } from '@angular/core';
import { LocalStorageService } from './local-storage.service';

export interface GroupAssignment {
  userId: string;
  group: 'WITH_AI' | 'WITHOUT_AI';
  assignedAt: number;
}

export interface GroupStats {
  withAI: number;
  withoutAI: number;
  total: number;
}

@Injectable({
  providedIn: 'root'
})
export class GroupAssignmentService {
  private readonly GROUP_ASSIGNMENTS_KEY = 'group_assignments';
  private readonly USER_GROUP_KEY = 'user_experiment_group';

  constructor(private localStorage: LocalStorageService) {}

  /**
   * Assigns a user to a group using balanced random assignment
   * Ensures equal distribution between groups
   */
  assignUserToGroup(userId: string): 'WITH_AI' | 'WITHOUT_AI' {
    // Check if user already has a group assignment
    const existingGroup = this.getUserGroup(userId);
    if (existingGroup) {
      return existingGroup;
    }

    // Get current group statistics
    const stats = this.getGroupStats();
    
    let assignedGroup: 'WITH_AI' | 'WITHOUT_AI';
    
    // Balanced assignment logic
    if (stats.withAI < stats.withoutAI) {
      assignedGroup = 'WITH_AI';
    } else if (stats.withoutAI < stats.withAI) {
      assignedGroup = 'WITHOUT_AI';
    } else {
      // Equal numbers, randomly assign
      assignedGroup = Math.random() < 0.5 ? 'WITH_AI' : 'WITHOUT_AI';
    }

    // Save the assignment
    this.saveGroupAssignment(userId, assignedGroup);
    
    return assignedGroup;
  }

  /**
   * Gets the group assignment for a specific user
   */
  getUserGroup(userId: string): 'WITH_AI' | 'WITHOUT_AI' | null {
    const assignments = this.getAllGroupAssignments();
    const userAssignment = assignments.find(assignment => assignment.userId === userId);
    return userAssignment ? userAssignment.group : null;
  }

  /**
   * Sets the user's group manually (for the radio button selection)
   */
  setUserGroup(userId: string, group: 'WITH_AI' | 'WITHOUT_AI'): void {
    this.saveGroupAssignment(userId, group);
    // Also save to user-specific localStorage key for quick access
    this.localStorage.setItem(this.USER_GROUP_KEY, group);
  }

  /**
   * Gets the current user's group from localStorage
   */
  getCurrentUserGroup(): 'WITH_AI' | 'WITHOUT_AI' | null {
    return this.localStorage.getItem(this.USER_GROUP_KEY) as 'WITH_AI' | 'WITHOUT_AI' | null;
  }

  /**
   * Checks if current user should see AI assistance
   */
  shouldShowAIAssistance(): boolean {
    const group = this.getCurrentUserGroup();
    return group === 'WITH_AI';
  }

  /**
   * Gets statistics about group distribution
   */
  getGroupStats(): GroupStats {
    const assignments = this.getAllGroupAssignments();
    const withAI = assignments.filter(a => a.group === 'WITH_AI').length;
    const withoutAI = assignments.filter(a => a.group === 'WITHOUT_AI').length;
    
    return {
      withAI,
      withoutAI,
      total: withAI + withoutAI
    };
  }

  /**
   * Saves a group assignment
   */
  private saveGroupAssignment(userId: string, group: 'WITH_AI' | 'WITHOUT_AI'): void {
    const assignments = this.getAllGroupAssignments();
    
    // Remove existing assignment for this user
    const filteredAssignments = assignments.filter(a => a.userId !== userId);
    
    // Add new assignment
    const newAssignment: GroupAssignment = {
      userId,
      group,
      assignedAt: Date.now()
    };
    
    filteredAssignments.push(newAssignment);
    
    // Save to localStorage
    this.localStorage.setItem(this.GROUP_ASSIGNMENTS_KEY, JSON.stringify(filteredAssignments));
    
    // Also save to user-specific key for quick access
    this.localStorage.setItem(this.USER_GROUP_KEY, group);
  }

  /**
   * Gets all group assignments from localStorage
   */
  private getAllGroupAssignments(): GroupAssignment[] {
    const assignmentsJson = this.localStorage.getItem(this.GROUP_ASSIGNMENTS_KEY);
    return assignmentsJson ? JSON.parse(assignmentsJson) : [];
  }

  /**
   * Clears all group assignments (for testing purposes)
   */
  clearAllAssignments(): void {
    this.localStorage.removeItem(this.GROUP_ASSIGNMENTS_KEY);
    this.localStorage.removeItem(this.USER_GROUP_KEY);
  }
}
