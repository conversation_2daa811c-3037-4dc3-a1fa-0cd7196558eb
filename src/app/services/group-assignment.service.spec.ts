import { TestBed } from '@angular/core/testing';
import { GroupAssignmentService } from './group-assignment.service';
import { LocalStorageService } from './local-storage.service';

describe('GroupAssignmentService', () => {
  let service: GroupAssignmentService;
  let localStorageService: jasmine.SpyObj<LocalStorageService>;

  beforeEach(() => {
    const localStorageSpy = jasmine.createSpyObj('LocalStorageService', ['getItem', 'setItem', 'removeItem']);

    TestBed.configureTestingModule({
      providers: [
        GroupAssignmentService,
        { provide: LocalStorageService, useValue: localStorageSpy }
      ]
    });

    service = TestBed.inject(GroupAssignmentService);
    localStorageService = TestBed.inject(LocalStorageService) as jasmine.SpyObj<LocalStorageService>;
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should assign user to group when no existing assignment', () => {
    localStorageService.getItem.and.returnValue(null);
    
    const group = service.assignUserToGroup('test-user');
    
    expect(group).toMatch(/^(WITH_AI|WITHOUT_AI)$/);
    expect(localStorageService.setItem).toHaveBeenCalled();
  });

  it('should return existing group assignment', () => {
    const existingAssignments = JSON.stringify([
      { userId: 'test-user', group: 'WITH_AI', assignedAt: Date.now() }
    ]);
    localStorageService.getItem.and.returnValue(existingAssignments);
    
    const group = service.getUserGroup('test-user');
    
    expect(group).toBe('WITH_AI');
  });

  it('should balance group assignments', () => {
    // Mock existing assignments with more users in WITH_AI group
    const existingAssignments = JSON.stringify([
      { userId: 'user1', group: 'WITH_AI', assignedAt: Date.now() },
      { userId: 'user2', group: 'WITH_AI', assignedAt: Date.now() },
      { userId: 'user3', group: 'WITHOUT_AI', assignedAt: Date.now() }
    ]);
    localStorageService.getItem.and.returnValue(existingAssignments);
    
    const group = service.assignUserToGroup('new-user');
    
    // Should assign to WITHOUT_AI to balance the groups
    expect(group).toBe('WITHOUT_AI');
  });

  it('should check if user should show AI assistance', () => {
    localStorageService.getItem.and.returnValue('WITH_AI');
    
    const shouldShow = service.shouldShowAIAssistance();
    
    expect(shouldShow).toBe(true);
  });

  it('should not show AI assistance for WITHOUT_AI group', () => {
    localStorageService.getItem.and.returnValue('WITHOUT_AI');
    
    const shouldShow = service.shouldShowAIAssistance();
    
    expect(shouldShow).toBe(false);
  });

  it('should calculate group statistics correctly', () => {
    const assignments = JSON.stringify([
      { userId: 'user1', group: 'WITH_AI', assignedAt: Date.now() },
      { userId: 'user2', group: 'WITH_AI', assignedAt: Date.now() },
      { userId: 'user3', group: 'WITHOUT_AI', assignedAt: Date.now() }
    ]);
    localStorageService.getItem.and.returnValue(assignments);
    
    const stats = service.getGroupStats();
    
    expect(stats.withAI).toBe(2);
    expect(stats.withoutAI).toBe(1);
    expect(stats.total).toBe(3);
  });
});
