<h1>Bienvenue sur la page sandbox</h1>
<p>Une page qui vous aidera à comprendre le fonctionnement du logiciel</p>

<hr>
<h2> Présentation du logiciel </h2>
<p>
  Le but du logiciel est de détecter des bruits dans un spectrogramme et de placer des boîtes englobantes aux endroits appropriés.<br>
  Pour ce faire, cinq types de bruits peuvent être détectés :
</p>
<ul>
  <li class="list-item"> Hélice :
    <img class="icon-bruiteur" src="img/icon/propeller.png" (click)="playSound('sound/Helice.wav')">
  </li>
  <li> Brouilleur :
    <img class="icon-bruiteur" src="img/icon/no-internet.png" (click)="playSound('sound/Brouilleur.wav')">
  </li>
  <li> Sonar,
    SonarCW : <img class="icon-bruiteur" src="img/icon/submarine.png" (click)="playSound('sound/SonarCW.wav')">
    SonarFM1 : <img class="icon-bruiteur" src="img/icon/submarine.png" (click)="playSound('sound/SonarFM1.wav')">
    SonarFM2 : <img class="icon-bruiteur" src="img/icon/submarine.png" (click)="playSound('sound/SonarFM2.wav')"></li>
</ul>

<p>
  Vous aurez la possibilité de choisir parmi ces différents types de bruits, ainsi qu'une classe de rejet <br>
  pour les bruits inconnus et les fausses alarmes. <br>
  Chaque mission a un temps donné et à la fin de ce temps vous ne pouvez plus modifier vos annotations ni revenir sur le spectrogramme.
</p>

<hr>

<h2>Fonctionnement du logiciel</h2>
<p>Lorsque vous arrivez sur la page, il y a plusieurs choses à regarder et à comprendre :</p>
<ul>
  <li>Le spectrogramme, qui englobe une grande partie</li>
  <li>La barre de navigation contient le bouton d'annotation (voir chapitre annotation), <br> le bouton pour activer ou non l'aide intelligente (voir chapitre aide intelligente).</li>
  <li>Le compteur</li>
  <li>Le rectangle d'information affiche les informations de temps et de fréquence en <br> suivant votre curseur de souris.</li>
</ul>


<img src="img/Presentation1/Slide1.PNG">
<img src=" img/Presentation1/Slide2.PNG">



<hr>

<h2>Utilisation de la Souris </h2>
<p>
  La souris est principalement utilisée pour zoomer en utilisant la molette. <br>
  En mode Zoom, il est possible de déplacer l'image en maintenant le clic gauche de la souris enfoncé. <br>
  En utilisant la molette, on peut créer un point, et en effectuant un deuxième clic, on peut démarrer un audio. (chapitre audio) </p>

<hr>

<h2>Annotation</h2>
<p> Une fois que vous pensez avoir identifié un bruit, vous pouvez l’annoter. <br>
Pour accomplir cela, il vous suffit de cliquer sur le bouton "Annoter" dans la barre de navigation.  <br>
Après avoir cliqué dessus, revenez sur le spectrogramme et maintenez le clic gauche à l'endroit souhaité. <br> Faites glisser votre souris pour entourer le bruiteur.
Une fois que le bruiteur est englobé, relâchez le clic.</p>

<img src="img/Presentation1/Slide3.PNG">
<h3> Formulaire annotation</h3>
<p>
  Dès que vous relâchez le clic de votre souris, le formulaire d'annotation s'ouvre. <br>
  Il doit être situé en haut à gauche de votre écran. Veuillez renseigner le type de <br>
  bruiteur et le pourcentage de confiance que vous avez dans votre choix. <br>
  Après avoir complété le formulaire, il vous suffit de cliquer sur confirmer. <br> La fenêtre se refermera et votre annotation
  sera maintenant incluse dans le spectrogramme et enregistrée en base de données.
</p>
<img src="img/Presentation1/Slide4.PNG">
<img src="img/Presentation1/Slide5.PNG" alt="annotation-creator">

<hr>

<h2>Audio</h2>
<p> Pour lancer l’audio rien de plus simple. Positionner votre souris sur la fréquence et le temps <br>
  que vous souhaitez et faites un clic molette. Un point blanc devra apparaitre. <br>
  Déplacer votre souris à un autre endroit afin d’englober un ou plusieurs bruiteurs. <br>
  Normalement une fenêtre en bas devra s’ouvrir avec l’audio qui se lancera automatiquement. <br>
  Sur cette fenêtre en rose vous trouverez l’audio avec une barre qui fait défiler l’audio. <br>
  Vous trouverez aussi un bouton «Play / pause» qui permet de mettre en pause ou de relancer l’audio. Vous pouvez revenir <br>
  en arrière ou avancer en cliquant sur la barre.
</p>
<img src="img/Presentation1/Slide7.PNG">
<img src="img/Presentation1/Slide8.PNG">
<img src="img/Presentation1/Slide9.PNG">
<hr>

<h2>Modification des annotations</h2>
<p>
  Vous n’êtes pas sûr d’une annotation et vous souhaitez la modifier  <br>
  Passez votre souris au-dessus d’un rectangle et vous verrez des informations apparaitre comme le type de bruiteur et <br>
  la confiance. Ce sont les informations que vous avez remplies au préalable. Si vous le désirez, il vous suffit de <br>
  cliquer n’importe où dans le rectangle avec le clic gauche pour ouvrir le formulaire d’annotation et ainsi modifier le résultat.

</p>

<hr>

<h2>Utilisation de l’intelligence artificielle </h2>
<p>Cliquer sur le bouton « toggle switch » en haut à droite. Toutes les annotations de de l'IA apparaitront en bleu. <br>
  Vous pourrez alors passer votre souris par-dessus pour voir les informations (bruiteur et confiance). Comme pour une <br>
  annotation normale vous pouvez décider de la modifier en cliquant dessus ou alors de ne rien faire. <br>
  Il se peut qu’elle se trompe</p>

<img src="img/Presentation1/Slide6.PNG">

<hr>

<h2>Combinaison des fonctionnalités ensembles</h2>
<p>Bien sûr, il est fortement recommandé d’utiliser toutes les fonctionnalités ensemble. C’est-à-dire commencer à chercher <br>
  par vous-même des bruiteurs. Si vous êtes bloqué, essayez d’utiliser le mode IA ou alors activer l’audio.
</p>

<hr>

<h2>Autres informations </h2>
<p> Vous avez peut-être remarqué plusieurs niveaux de couleurs pour les annotations. <br>
Le blanc répresente les annotations que vous avez créées ou acceptées. Les annotations <br>
proposées par l'IA sont affichées en bleu. Si vous acceptez ou modifier une annotation suggérée par l’IA, cell-ci deviendra blanche.
</p>
<hr>

<div class="button-back">
  <button class="button" type="button" (click)="goToHome()">
    <div class="icon-container">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
        <path d="M224 480h640a32 32 0 1 1 0 64H224a32 32 0 0 1 0-64z"></path>
        <path d="m237.248 512 265.408 265.344a32 32 0 0 1-45.312 45.312l-288-288a32 32 0 0 1 0-45.312l288-288a32 32 0 1 1 45.312 45.312L237.248 512z"></path>
      </svg>
    </div>
    <p class="back"> Retour en arrière</p>
  </button>
</div>


