/**
 * Simple integration test for clickstream functionality
 * This file can be imported and run to verify the clickstream implementation works
 */

import { ClickstreamService } from './services/clickstream.service';
import { ClickstreamStorageService } from './services/clickstream-storage.service';
import { LocalStorageService } from './services/local-storage.service';
import { ClickstreamEventType } from './models/clickstream.models';

export class ClickstreamIntegrationTest {
  private clickstreamService: ClickstreamService;
  private storageService: ClickstreamStorageService;
  private localStorageService: LocalStorageService;

  constructor() {
    // Mock localStorage service for testing
    this.localStorageService = {
      getItem: (key: string) => {
        const mockData: Record<string, string> = {
          'uniqueId': 'test-user-123',
          'spectroName': 'test-spectro.png'
        };
        return mockData[key] || null;
      },
      setItem: (key: string, value: string) => {
        console.log(`Mock localStorage.setItem: ${key} = ${value}`);
      },
      removeItem: (key: string) => {
        console.log(`Mock localStorage.removeItem: ${key}`);
      },
      clearItem: () => {
        console.log('Mock localStorage.clear()');
      }
    } as LocalStorageService;

    this.clickstreamService = new ClickstreamService(this.localStorageService);
    this.storageService = new ClickstreamStorageService(null as any, this.localStorageService);
  }

  async runTests(): Promise<boolean> {
    console.log('🧪 Starting Clickstream Integration Tests...\n');

    try {
      await this.testBasicEventTracking();
      await this.testAnnotationFormTracking();
      await this.testReflectionTimeCalculation();
      await this.testDataExport();
      await this.testAnalyticsCalculation();

      console.log('✅ All tests passed!\n');
      return true;
    } catch (error) {
      console.error('❌ Test failed:', error);
      return false;
    }
  }

  private async testBasicEventTracking(): Promise<void> {
    console.log('📝 Testing basic event tracking...');

    // Track a simple click event
    this.clickstreamService.trackEvent(ClickstreamEventType.MOUSE_CLICK, {
      elementId: 'test-button',
      metadata: { x: 100, y: 200 }
    });

    // Track a page load event
    this.clickstreamService.trackEvent(ClickstreamEventType.PAGE_LOADED, {
      elementId: 'spectro-page'
    });

    const exportData = this.clickstreamService.exportData();
    
    if (exportData.events.length < 2) {
      throw new Error('Events not being tracked properly');
    }

    console.log(`   ✓ Tracked ${exportData.events.length} events`);
  }

  private async testAnnotationFormTracking(): Promise<void> {
    console.log('📝 Testing annotation form tracking...');

    const rectangleData = {
      startX: 100,
      startY: 100,
      width: 50,
      height: 50,
      bruiteur: '',
      trust: 0
    };

    // Start annotation form
    this.clickstreamService.startAnnotationForm(rectangleData);
    
    // Simulate some delay
    await this.delay(100);
    
    // Track field interactions
    this.clickstreamService.trackFieldFocus('bruiteur', '');
    this.clickstreamService.trackFieldChange('bruiteur', 'Hélice', '');
    this.clickstreamService.trackFieldBlur('bruiteur', 'Hélice');
    
    this.clickstreamService.trackFieldFocus('trust', 0);
    this.clickstreamService.trackFieldChange('trust', 75, 0);
    this.clickstreamService.trackFieldBlur('trust', 75);

    // End annotation form
    this.clickstreamService.endAnnotationForm('submitted', {
      bruiteur: 'Hélice',
      trust: 75
    });

    const currentReflectionTime = this.clickstreamService.getCurrentReflectionTime();
    
    if (currentReflectionTime !== 0) {
      throw new Error('Reflection time should be reset after form submission');
    }

    console.log('   ✓ Annotation form tracking completed');
  }

  private async testReflectionTimeCalculation(): Promise<void> {
    console.log('📝 Testing reflection time calculation...');

    // Start a new annotation
    this.clickstreamService.startAnnotationForm();
    
    // Wait a bit to accumulate reflection time
    await this.delay(50);
    
    const reflectionTime = this.clickstreamService.getCurrentReflectionTime();
    
    if (reflectionTime <= 0) {
      throw new Error('Reflection time should be greater than 0');
    }

    // End the annotation
    this.clickstreamService.endAnnotationForm('cancelled');

    console.log(`   ✓ Reflection time calculated: ${reflectionTime}ms`);
  }

  private async testDataExport(): Promise<void> {
    console.log('📝 Testing data export...');

    const exportData = this.clickstreamService.exportData();
    
    if (!exportData.events || !exportData.session) {
      throw new Error('Export data missing required fields');
    }

    if (exportData.events.length === 0) {
      throw new Error('No events in export data');
    }

    console.log(`   ✓ Export data contains ${exportData.events.length} events`);
  }

  private async testAnalyticsCalculation(): Promise<void> {
    console.log('📝 Testing analytics calculation...');

    // Create some mock reflection time data
    const mockReflectionTimes = [
      {
        annotationId: '1',
        userId: 'test-user-123',
        sessionId: 'session-1',
        spectroName: 'test-spectro.png',
        formOpenedAt: Date.now() - 5000,
        formClosedAt: Date.now(),
        totalReflectionTime: 5000,
        bruiteurFieldTime: 2000,
        confidenceFieldTime: 1500,
        bruiteurChanges: 1,
        confidenceChanges: 2,
        finalBruiteur: 'Hélice',
        finalConfidence: 75,
        wasSubmitted: true,
        wasCancelled: false,
        wasDeleted: false
      },
      {
        annotationId: '2',
        userId: 'test-user-123',
        sessionId: 'session-1',
        spectroName: 'test-spectro.png',
        formOpenedAt: Date.now() - 8000,
        formClosedAt: Date.now() - 3000,
        totalReflectionTime: 3000,
        bruiteurFieldTime: 1500,
        confidenceFieldTime: 1000,
        bruiteurChanges: 0,
        confidenceChanges: 1,
        finalBruiteur: 'SonarCW',
        finalConfidence: 100,
        wasSubmitted: true,
        wasCancelled: false,
        wasDeleted: false
      }
    ];

    // Mock the local storage to return our test data
    const originalGetItem = this.localStorageService.getItem;
    this.localStorageService.getItem = (key: string) => {
      if (key === 'reflection_times') {
        return JSON.stringify(mockReflectionTimes);
      }
      return originalGetItem.call(this.localStorageService, key);
    };

    const analytics = this.storageService.calculateLocalAnalytics('test-user-123', 'test-spectro.png');

    if (analytics.totalAnnotations !== 2) {
      throw new Error(`Expected 2 annotations, got ${analytics.totalAnnotations}`);
    }

    if (analytics.averageReflectionTime !== 4000) {
      throw new Error(`Expected average reflection time of 4000ms, got ${analytics.averageReflectionTime}ms`);
    }

    if (analytics.mostCommonBruiteur !== 'Hélice' && analytics.mostCommonBruiteur !== 'SonarCW') {
      throw new Error(`Unexpected most common bruiteur: ${analytics.mostCommonBruiteur}`);
    }

    // Restore original method
    this.localStorageService.getItem = originalGetItem;

    console.log('   ✓ Analytics calculation completed');
    console.log(`   ✓ Average reflection time: ${analytics.averageReflectionTime}ms`);
    console.log(`   ✓ Total annotations: ${analytics.totalAnnotations}`);
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Export function to run tests
export async function runClickstreamTests(): Promise<boolean> {
  const tester = new ClickstreamIntegrationTest();
  return await tester.runTests();
}

// If running directly in browser console:
// runClickstreamTests().then(success => console.log('Tests completed:', success));
