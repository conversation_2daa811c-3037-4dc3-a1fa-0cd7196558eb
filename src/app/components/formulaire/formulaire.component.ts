import { Component } from '@angular/core';
import {Router, RouterLink} from "@angular/router";
import {FormBuilder, FormsModule} from '@angular/forms';
import { NgIf } from '@angular/common';
import {UserServiceService} from "../../services/user-service.service";
import {LocalStorageService} from "../../services/local-storage.service";

@Component({
  selector: 'app-formulaire',
  standalone: true,
  imports: [FormsModule,NgIf],
  templateUrl: './formulaire.component.html',
  styleUrl: './formulaire.component.css'
})

export class FormulaireComponent {
  constructor(private userService: UserServiceService, private router: Router,
  private localService: LocalStorageService) {
  }
  creatingLogin = false;
  loggingIn = false;
  newLogin = '';
  enteredLogin = '';
  message = '';

  showCreateLogin() {
    this.creatingLogin = true;
    this.loggingIn = false;
    this.message = '';
  }

  showLogin() {
    this.loggingIn = true;
    this.creatingLogin = false;
    this.message = '';
  }

  generateRandomNumber(): number {
    return Math.floor(1000 + Math.random() * 9000); // Generates a 4-digit number (1000-9999)
  }

  saveLogin() {
    if (this.newLogin.trim()) {
      const uniqueLogin = `${this.newLogin.trim()}${this.generateRandomNumber()}`;
      // localStorage.setItem('userLogin', uniqueLogin);
      console.log(uniqueLogin);

      this.userService.createUser(uniqueLogin).subscribe((result) => {
        this.message = `Your login is: ${uniqueLogin}`;
        this.newLogin = '';
      });
    } else {
      this.message = 'Please enter a valid word.';
    }
  }

  connect() {
    console.log('Connecting...');
    if(this.enteredLogin.trim()) {
      this.localService.setItem("uniqueId", this.enteredLogin);
      this.userService.connect(this.enteredLogin.trim()).subscribe((result) => {
       this.message = `Login succesfull`;
       console.log(result);
       this.router.navigate(['/home']);
     },
       error => {
       if (error.status === 401) {
         this.message = 'Authentication failed';
       } else {
         this.message = 'An error occurred. PLease try again';
       }
     });
   }
  }

}
