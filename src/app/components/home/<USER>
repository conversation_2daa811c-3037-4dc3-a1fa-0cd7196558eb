body {
  overflow:hidden;
  margin: 0;
}

.view {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.left, .right {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 50%;
  overflow: hidden;
}

.left {
  left: 0;
  clip-path: polygon(0 0, 100% 0, 100% 120%, 0 100%);
}

.right {
  right: 0;
  clip-path: polygon(0 -20%, 100% 0, 100% 100%, 0 100%);
}

.divider {
  background-color: black;
  width: 8px;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 50.15%;
  transform: translateX(-50%);
  z-index: 1;
}

.spectro, .sandbox {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
}

.spectro {
  background-image: url('/img/spectrogram.png');

}

.sandbox {
  background-image: url('/img/sand.jpg');
}

h1 {
  position: absolute;
  top: 30%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 36px;
  text-align: center;
}

.title-right{
  color: black;
}
.btn-left{
  color: white;
  border: 2px solid white;
}
.btn-left:hover{
  background-color: black;
  color: white;
}

.btn-right {
  color: black;
  border: 2px solid black;
}
.btn-right:hover{
  background-color: white;
  color: black;
}

button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 10px 20px;
  background-color: transparent;
  font-size: 20px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;

}

button:hover {
  background-color: white;
  color: black;
}


.welcome-message {
  position: absolute;
  top: 10px;
  right: 10px;
  text-align: right;
  color: black;
  font-family: Arial, sans-serif;
}

.welcome-message {
  font-size: 1rem;
  font-weight: bold;
}

.welcome-header {
  font-size: 0.9rem;
  margin-top: 5px;
}
