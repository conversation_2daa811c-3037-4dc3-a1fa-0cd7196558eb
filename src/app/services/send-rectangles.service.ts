import { Injectable } from '@angular/core';
import {HttpClient, HttpHeaders} from '@angular/common/http';
import {Observable} from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class SendRectanglesService {
  private apiUrl = 'http://localhost:8000/rectangles';  // URL de votre API FastAPI

  constructor(private http: HttpClient) {}

  saveAnnotation(annotationData: any): Observable<any> {
    const headers = new HttpHeaders({ 'Content-Type': 'application/json' });
    return this.http.post<any>(this.apiUrl, annotationData, { headers });
  }
}
