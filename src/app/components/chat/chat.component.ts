import { Component } from '@angular/core';
import {FormsModule} from "@angular/forms";
import {NgForO<PERSON>, NgIf} from "@angular/common";

@Component({
  selector: 'app-chat',
  standalone: true,
  imports: [
    FormsModule,
    NgIf,
    NgForOf
  ],
  templateUrl: './chat.component.html',
  styleUrl: './chat.component.css'
})
export class ChatComponent {
  isCollapsed = true; // État du chat (ouvert/fermé)
  messages: { sender: string; text: string }[] = []; // Liste des messages
  newMessage: string = ''; // Message en cours de saisie

  toggleChat() {
    this.isCollapsed = !this.isCollapsed; // Basculer l'état du chat
  }

  sendMessage() {
    if (this.newMessage.trim()) {
      this.messages.push({ sender: 'Vous', text: this.newMessage.trim() });
      this.newMessage = '';
    }
  }

}
