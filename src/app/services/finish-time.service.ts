import { Injectable } from '@angular/core';
import {HttpClient} from "@angular/common/http";
import {map, Observable} from "rxjs";

export interface IFinishTime {
  finish_time: number;
}

export interface ISpectroId {
  spectro_id: number;
}

@Injectable({
  providedIn: 'root'
})
export class FinishTimeService {
  private apiUrl = 'http://localhost:8000';

  constructor(private http: HttpClient) { }

  getFinishTime(name_spectro: string | null): Observable<string>{
    return this.http.get<string>(`${this.apiUrl}/spectro/${name_spectro}/time`);
  }

  timeStringToMilliseconds(time: string): number {
    const [hours, minutes, seconds] = time.split(':').map(Number);
    return ((hours * 3600) + (minutes * 60 ) + seconds) * 1000;
  }

  getTimeALLMission(name_spectro: string | null): Observable<string>{
    return this.http.get<string>(`${this.apiUrl}/spectro/time_mission_real?spectro_name=${name_spectro}`);
  }
}
