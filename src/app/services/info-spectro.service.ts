import { Injectable } from '@angular/core';
import {HttpClient} from "@angular/common/http";
import {map, Observable} from "rxjs";
import {IFinishTime} from "./finish-time.service";

export interface AttribuSpectro {
  id? : number;
  temps_mission : number;
  array_bruiteur: string[];
  id_attribut : number;
  nb_bruiteur: number
}

@Injectable({
  providedIn: 'root'
})

export class InfoSpectroService {
  private apiUrl = 'http://localhost:8000/info_spectro/?info_name_spectro=';
  private apiGetId = 'http://localhost:8000/spectro_id/?name='

  constructor(private http: HttpClient) { }

  getInfoSpectro(name_spectro: string | null): Observable<AttribuSpectro> {
    return this.http.get<AttribuSpectro>(`${this.apiUrl}${name_spectro}`);
  }

  getImageId(imageName: string | null): Observable<number> {
    return this.http.get<{image_id: number}>(`${this.apiGetId}${imageName}`).pipe(
      map(response => response.image_id)
    );
  }
}
