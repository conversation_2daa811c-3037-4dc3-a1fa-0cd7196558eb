{\rtf1\ansi\ansicpg1252\cocoartf2821
\cocoatextscaling0\cocoaplatform0{\fonttbl\f0\fswiss\fcharset0 Helvetica;\f1\fmodern\fcharset0 Courier;\f2\fmodern\fcharset0 Courier-Oblique;
}
{\colortbl;\red255\green255\blue255;\red203\green170\blue101;\red23\green23\blue26;\red185\green101\blue173;
\red89\green158\blue96;\red32\green34\blue36;\red174\green176\blue183;\red195\green123\blue90;}
{\*\expandedcolortbl;;\csgenericrgb\c79608\c66667\c39608;\csgenericrgb\c9020\c9020\c10196;\csgenericrgb\c72549\c39608\c67843;
\csgenericrgb\c34902\c61961\c37647;\csgenericrgb\c12549\c13333\c14118;\csgenericrgb\c68235\c69020\c71765;\csgenericrgb\c76471\c48235\c35294;}
\margl1440\margr1440\vieww11520\viewh8400\viewkind0
\pard\tx720\tx1440\tx2160\tx2880\tx3600\tx4320\tx5040\tx5760\tx6480\tx7200\tx7920\tx8640\pardirnatural\partightenfactor0

\f0\fs24 \cf0 Test html \
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\pardirnatural\partightenfactor0

\f1\fs26 \cf2 \cb3 <app-audio-player 
\f2\i \cf4 *ngIf
\f1\i0 \cf5 ="\cf4 \cb6 isAudioPlayerVisible\cf5 \cb3 "\cf2 ></app-audio-player>\
\
\
\cf4 isAudioPlayerVisible \cf7 = \cf8 false\cf7 ;\
\cf4 isPlaying \cf7 = \cf8 false\cf7 ;\
\
}