/* Rectangle container */
#player-container {
  width: 560px;
  height: 165px;
  background: #222;
  border-radius: 10px;
  padding: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.3);
  position: fixed;
  bottom: 0;
  z-index: 99999;
  left: 50%;
  transform: translateX(-50%);
}

/* Waveform styling */
#waveform {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 50px;
  margin-top: auto;
}

/* Time display */
#time-container {
  font-size: 14px;
  color: white;
  font-family: Arial, sans-serif;
}

/* Play/Pause button */
#play-btn {
  margin-top: 10px;
  padding: 10px 20px;
  background: #ff0077;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
}

#play-btn:hover {
  background: #cc005f;
}

.close-btn-player {
  position: absolute;
  top: 10px;
  right: 10px;
  color: red;
  border: none;
  cursor: pointer;
  font-size: 20px;
}

.spanAudio {
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 18px;
}
