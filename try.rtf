{\rtf1\ansi\ansicpg1252\cocoartf2821
\cocoatextscaling0\cocoaplatform0{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
{\*\expandedcolortbl;;}
\margl1440\margr1440\vieww28300\viewh17700\viewkind0
\pard\tx720\tx1440\tx2160\tx2880\tx3600\tx4320\tx5040\tx5760\tx6480\tx7200\tx7920\tx8640\pardirnatural\partightenfactor0

\f0\fs24 \cf0 import \{\
  Component,\
  Input,\
  AfterViewInit,\
  OnChanges,\
  SimpleChanges,\
  ViewChild,\
  ElementRef,\
  OnInit,\
  HostListener\
\} from '@angular/core';\
import \{TopbarComponent\} from "../topbar/topbar.component";\
import \{TimerComponent\} from "../timer/timer.component";\
import \{ PinchZoomComponent \} from '@meddv/ngx-pinch-zoom';\
import \{DrawingService\} from '../../services/drawing.service';\
import \{AnnotationCreatorComponent\} from '../annotation-creator/annotation-creator.component';\
import \{NgIf\} from '@angular/common';\
\
interface Rectangle \{\
  startX: number;\
  startY: number;\
  width: number;\
  height: number;\
  name: string;\
  percentage: number;\
  comments: string;\
\}\
\
@Component(\{\
  selector: 'app-test',\
  standalone: true,\
  imports: [TopbarComponent,\
    TimerComponent,\
    PinchZoomComponent,\
    AnnotationCreatorComponent,\
    NgIf],\
  templateUrl: './test.component.html',\
  styleUrls: ['./test.component.css']\
\})\
export class TestComponent implements AfterViewInit, OnInit \{\
  @ViewChild('imageCanvas', \{ static: true \}) canvasRef!: ElementRef<HTMLCanvasElement>;\
\
  cursorX: number = 0;\
  cursorY: number = 0;\
\
  frequency: number = 0;\
  time : number = 0;\
\
  Maxfrequency = 22050;\
  timeMinutes = 60;\
  maxTime = this.timeMinutes * 60000;\
\
  private canvas!: HTMLCanvasElement;\
  private ctx!: CanvasRenderingContext2D;\
  private image!: HTMLImageElement;\
\
  private isDrawing = false;\
  private startX = 0;\
  private startY = 0;\
\
  protected rectangles: Rectangle[] = [];\
  private currentRectangle: Rectangle | null = null;\
\
  //  Middle mouse scroll\
  private isMiddleMouseDown = false;\
  private startScrollX = 0;\
  private scrollLeft = 0;\
\
  isAnnotationCreatorVisible = false;\
  constructor(private elementRef: ElementRef, private drawingService: DrawingService) \{ \}\
\
  ngAfterViewInit() \{\
    this.elementRef.nativeElement.ownerDocument.body.style.backgroundColor = 'black';\
  \}\
\
  ngOnInit(): void \{\
    this.initializeCanvas();\
  \}\
\
  initializeCanvas(): void \{\
    this.canvas = this.canvasRef.nativeElement\
    this.ctx = this.canvas.getContext('2d') as CanvasRenderingContext2D;\
\
    this.image = new Image();\
    this.image.src = 'img/spectro.png';\
    this.image.onload = () => \{\
      const topbarHeight = this.elementRef.nativeElement.querySelector('app-topbar').offsetHeight;\
      const timerHeight = this.elementRef.nativeElement.querySelector('app-timer').offsetHeight;\
      const availableHeight = window.innerHeight - topbarHeight - timerHeight;\
\
      this.canvas.width = this.image.width;\
      this.canvas.height = Math.min(this.image.height, availableHeight);\
\
      this.ctx.drawImage(this.image, 0, 0, this.canvas.width, this.canvas.height);\
    \};\
  \}\
\
  @HostListener('mousedown', ['$event'])\
  onMouseDown(event: MouseEvent): void \{\
    if (event.button === 1) \{ // Middle mouse button\
      this.startMiddleMouseScroll(event);\
    \} else if (this.drawingService.isDrawingMode()) \{\
      this.startDrawing(event);\
    \}\
  \}\
\
  @HostListener('mousemove', ['$event'])\
  onMouseMove(event: MouseEvent): void \{\
    const rect = this.canvas.getBoundingClientRect();\
    const cursorX = event.clientX - rect.left;\
    const cursorY = event.clientY - rect.top;\
\
    if (this.isMiddleMouseDown) \{\
      this.handleMiddleMouseScroll(event);\
    \} else if (this.isDrawing) \{\
      this.handleRectangleDrawing(cursorX, cursorY);\
    \}\
\
    this.handleCursorCalculations(cursorX, cursorY);\
  \}\
\
  @HostListener('mouseup', ['$event'])\
  onMouseUp(event: MouseEvent): void \{\
    if (event.button === 1) \{ // Middle mouse button\
      this.stopMiddleMouseScroll();\
    \} else if (this.isDrawing) \{\
      this.stopDrawing();\
    \}\
  \}\
\
  @HostListener('mouseleave', ['$event'])\
  onMouseLeave(): void \{\
    this.isMiddleMouseDown = false;\
    this.isDrawing = false;\
  \}\
\
  private startMiddleMouseScroll(event: MouseEvent): void \{\
    this.isMiddleMouseDown = true;\
    this.startScrollX = event.clientX;\
    this.scrollLeft = this.canvas.scrollLeft;\
    event.preventDefault();\
  \}\
\
  private handleMiddleMouseScroll(event: MouseEvent): void \{\
    const x = event.clientX - this.startScrollX;\
    this.canvas.scrollLeft = this.scrollLeft - x;\
    event.preventDefault();\
  \}\
\
  private stopMiddleMouseScroll(): void \{\
    this.isMiddleMouseDown = false;\
  \}\
\
  private startDrawing(event: MouseEvent): void \{\
    this.isDrawing = true;\
    const rect = this.canvasRef.nativeElement.getBoundingClientRect();\
    this.startX = event.clientX - rect.left;\
    this.startY = event.clientY - rect.top;\
\
    this.currentRectangle = \{ startX: this.startX, startY: this.startY, width: 0, height: 0, name:'', percentage: 0, comments: '' \};\
    this.drawingService.setDrawingMode(false);\
  \}\
\
  private stopDrawing(): void \{\
    this.isDrawing = false;\
    if (this.currentRectangle) \{\
      this.rectangles.push(\{ ...this.currentRectangle \});\
      this.printAllRectangles('Rectangles after drawing');\
    \}\
    this.isAnnotationCreatorVisible = true;\
    this.currentRectangle = null;\
  \}\
\
  private handleRectangleDrawing(currentX: number, currentY: number): void \{\
    this.currentRectangle = \{\
      startX: this.startX,\
      startY: this.startY,\
      width: currentX - this.startX,\
      height: currentY - this.startY,\
      name: '',\
      percentage: 0,\
      comments: ''\
    \};\
\
    this.redrawCanvas();\
    this.drawRectangle(this.currentRectangle);\
  \}\
\
  private handleCursorCalculations(cursorX: number, cursorY: number): void \{\
    const cursorInfo = document.getElementById('cursor-info');\
    if (cursorX >= 0 && cursorX <= this.canvas.width && cursorY >= 0 && cursorY <= this.canvas.height) \{\
      this.cursorX = Math.floor(cursorX);\
      this.cursorY = Math.floor(cursorY);\
\
      this.frequency = (this.Maxfrequency * this.cursorX) / this.canvas.width;\
      this.time = this.maxTime * (1 - this.cursorY / this.canvas.height);\
\
      if (cursorInfo) \{\
        cursorInfo.style.left = `$\{cursorX + 20\}px`;\
        cursorInfo.style.top = `$\{cursorY + 100\}px`;\
        cursorInfo.style.display = 'block';\
        cursorInfo.innerHTML = `Frequency: $\{this.frequency.toFixed(2)\} Hz<br>Time: $\{(this.time / 60000).toFixed(2)\} m`;\
      \}\
    \} else \{\
      this.resetCursorPosition();\
      if (cursorInfo) \{\
        cursorInfo.style.display = 'none';\
      \}\
    \}\
  \}\
\
  private redrawCanvas(): void \{\
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);\
    this.ctx.drawImage(this.image, 0, 0, this.canvas.width, this.canvas.height);\
\
    this.rectangles.forEach(rect => \{\
      this.drawRectangle(rect);\
    \});\
\
    if (this.currentRectangle) \{\
      this.drawRectangle(this.currentRectangle);\
    \}\
  \}\
\
  private drawRectangle(rect: \{ startX: number; startY: number; width: number; height: number\}): void \{\
    const \{ startX, startY, width, height\} = rect;\
    this.ctx.beginPath();\
    this.ctx.rect(startX, startY, width, height);\
    this.ctx.strokeStyle = 'white';\
    this.ctx.lineWidth = 2;\
    this.ctx.stroke();\
  \}\
\
  resetCursorPosition(): void \{\
    this.cursorX = 0;\
    this.cursorY = 0;\
    this.frequency = 0;\
    this.time = 0;\
\
    const cursorInfo = document.getElementById('cursor-info');\
    if (cursorInfo) \{\
      cursorInfo.style.display = 'none';\
    \}\
  \}\
\
  printAllRectangles(text: string): void \{\
    console.log(text, this.rectangles);\
  \}\
\
  saveAnnotationCreator(annotation: \{ name: string; percentage: number; comments: string \}): void \{\
    if (this.rectangles.length > 0) \{\
      const lastRectangleIndex = this.rectangles.length - 1;\
      this.rectangles[lastRectangleIndex] = \{\
        ...this.rectangles[lastRectangleIndex],\
        name: annotation.name,\
        percentage: annotation.percentage,\
        comments: annotation.comments,\
      \};\
      this.printAllRectangles('Rectangles after saving annotation');\
    \}\
    this.isAnnotationCreatorVisible = false;\
    annotation.name = '';\
    annotation.percentage = undefined!;\
    annotation.comments = '';\
  \}\
\
  closeAnnotationCreator(): void \{\
    if (this.rectangles.length > 0) \{\
      this.rectangles.pop();\
    \}\
\
    this.redrawCanvas();\
    this.isAnnotationCreatorVisible = false;\
  \}\
\}\
\
\
\
\
html, body \{\
  margin: 0;\
  padding: 0;\
  overflow: hidden;\
  height: 100%;\
\}\
\
.image-container \{\
  flex: 1;\
  display: flex;\
  justify-content: center;\
  align-items: center;\
  overflow: hidden;\
\}\
\
canvas \{\
  display: block;\
  max-width: 100%;\
  max-height: 100%;\
  border: 1px solid #ccc;\
  cursor: crosshair;\
\}\
\
.cursor-info \{\
  position: absolute;\
  background-color: rgba(0, 0, 0, 0.7);\
  color: white;\
  padding: 10px;\
  border-radius: 3px;\
  pointer-events: none;\
  font-size: 16px;\
  z-index: 10;\
\}\
\
.page-container \{\
  display: flex;\
  flex-direction: column;\
  height: 100%;\
\}\
\
app-timer.timer-container \{\
  font-size: 50px;\
  font-weight: bold;\
  text-align: right;\
  padding: 10px 20px;\
  color: black;\
  background-color: #f8f9fa;\
  border-bottom: 2px solid #ddd;\
  position: sticky;\
  top: 0;\
  z-index: 10;\
\}\
\
app-topbar.navbar \{\
  list-style-type: none;\
  padding: 0;\
  margin: 0;\
  display: flex;\
  gap: 20px;\
\}\
\
.middle-mouse-dot \{\
  position: absolute;\
  width: 10px;\
  height: 10px;\
  background-color: orange;\
  border-radius: 50%;\
  z-index: 1000;\
  pointer-events: none;\
\}\
\
.middle-mouse-line \{\
  position: absolute;\
  width: 2px;\
  background-color: orange;\
  z-index: 1000;\
  pointer-events: none;\
\}\
\
}