<div id="player-container">
  <div #waveform id="waveform"></div>
  <div id="time-container">
    <span class="spanAudio">{{remaingTime.toFixed(2)}} / {{totalTime.toFixed(2)}}</span>
    <span class="spanAudio">Temps de début {{convertTimeToSeconds(this.startTime)}} / Temps de fin {{convertTimeToSeconds(this.endTime)}}</span>
  </div>
  <button
    id="play-btn"
    (click)="togglePlay()"
    [ngStyle]="{ 'background-color': isPlaying ? 'red' : 'green' }">
    {{ isPlaying ? '⏸ Pause' : '▶ Play' }}
  </button>
  <button class="close-btn-player" (click)="closePlayer()">x</button>
</div>
