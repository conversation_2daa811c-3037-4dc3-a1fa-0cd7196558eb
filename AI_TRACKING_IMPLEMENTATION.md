# AI Entity Tracking Implementation

## Overview
This document describes the implementation of AI entity tracking in the clickstream system, which measures user interactions with the AI assistant features.

## Features Implemented

### 1. AI Helper Toggle Tracking
- **Location**: Navigation bar toggle switch
- **Events Tracked**: 
  - `AI_HELPER_ACTIVATED` - When user turns on AI helper
  - `AI_HELPER_DEACTIVATED` - When user turns off AI helper
- **Implementation**: `trackAIHelperToggle()` method in `ClickstreamService`

### 2. AI Rectangle Click Tracking
- **Location**: Blue rectangles displayed when AI helper is active
- **Events Tracked**: 
  - `AI_RECTANGLE_CLICKED` - When user clicks on a blue AI rectangle
- **Implementation**: Enhanced `openAnnotationCreator()` method in `SpectroComponent`

### 3. AI Suggestion Interaction Tracking
- **Events Tracked**:
  - `AI_SUGGESTION_SHOWN` - When AI rectangles are displayed
  - `AI_SUGGESTION_ACCEPTED` - When user accepts AI suggestion
  - `AI_SUGGESTION_MODIFIED` - When user modifies AI suggestion
  - `AI_SUGGESTION_REJECTED` - When user rejects AI suggestion

### 4. Enhanced Analytics Dashboard
- **New Metrics Displayed**:
  - AI Helper Usage Count
  - AI Rectangle Click Count
  - Total AI Interactions
  - AI Suggestions Accepted/Modified/Rejected
  - Average AI Reflection Time
  - AI Acceptance/Modification/Rejection Rates

## Files Modified

### Core Services
1. **`src/app/services/clickstream.service.ts`**
   - Added `trackAIHelperToggle()` method
   - Added `trackAIRectangleClick()` method
   - Enhanced `isAiSuggestion()` detection logic

2. **`src/app/services/clickstream-storage.service.ts`**
   - Enhanced analytics calculation with AI metrics
   - Added AI helper activations and rectangle clicks counting
   - Updated empty analytics template

### Models
3. **`src/app/models/clickstream.models.ts`**
   - Added missing AI event types:
     - `AI_HELPER_ACTIVATED`
     - `AI_HELPER_DEACTIVATED` 
     - `AI_RECTANGLE_CLICKED`

### Components
4. **`src/app/components/spectro/spectro.component.ts`**
   - Enhanced `openAnnotationCreator()` to detect and track AI rectangle clicks
   - Added AI rectangle detection logic

5. **`src/app/components/reflection-analytics/reflection-analytics.component.html`**
   - Added comprehensive AI analytics section
   - Added AI performance rates with visual progress bars
   - Added AI summary cards in main dashboard

6. **`src/app/components/reflection-analytics/reflection-analytics.component.css`**
   - Added styles for AI performance section
   - Added visual progress bars for acceptance/modification/rejection rates
   - Added AI-specific color coding

## Usage Instructions

### For Users
1. **Activate AI Helper**: Click the toggle switch in the navigation bar
2. **View AI Suggestions**: Blue rectangles will appear on the spectrogram
3. **Interact with AI**: Click on blue rectangles to accept, modify, or reject suggestions
4. **View Analytics**: Navigate to the analytics dashboard to see AI interaction metrics

### For Developers
1. **Track Custom AI Events**: Use `clickstreamService.trackEvent()` with AI event types
2. **Access AI Metrics**: Use `storageService.calculateLocalAnalytics()` to get AI metrics
3. **Monitor Real-time**: Subscribe to `clickstreamService.events$` for real-time tracking

## Data Structure

### AI Metrics in Analytics
```typescript
interface ClickstreamAnalytics {
  // ... existing fields ...
  
  // AI interaction metrics
  aiHelperUsageCount: number;
  aiRectangleClickCount: number;
  aiSuggestionsAccepted: number;
  aiSuggestionsModified: number;
  aiSuggestionsRejected: number;
  averageAIReflectionTime: number;
  aiAcceptanceRate: number;
  aiModificationRate: number;
  aiRejectionRate: number;
  totalAIInteractions: number;
}
```

### AI Events Tracked
```typescript
enum ClickstreamEventType {
  AI_HELPER_ACTIVATED = 'ai_helper_activated',
  AI_HELPER_DEACTIVATED = 'ai_helper_deactivated',
  AI_RECTANGLE_CLICKED = 'ai_rectangle_clicked',
  AI_SUGGESTION_SHOWN = 'ai_suggestion_shown',
  AI_SUGGESTION_ACCEPTED = 'ai_suggestion_accepted',
  AI_SUGGESTION_MODIFIED = 'ai_suggestion_modified',
  AI_SUGGESTION_REJECTED = 'ai_suggestion_rejected'
}
```

## Testing

To test the AI tracking functionality:

1. **Start the application**
2. **Navigate to the spectrogram page**
3. **Toggle the AI helper on/off** - Check that activation/deactivation events are tracked
4. **Click on blue AI rectangles** - Verify rectangle click events are recorded
5. **Accept/modify/reject AI suggestions** - Confirm interaction tracking
6. **View analytics dashboard** - Verify all AI metrics are displayed correctly

## Benefits

1. **Comprehensive AI Usage Analytics**: Track how often users engage with AI features
2. **User Behavior Insights**: Understand acceptance vs rejection patterns
3. **Performance Metrics**: Measure AI suggestion effectiveness
4. **Reflection Time Analysis**: Compare reflection times with and without AI assistance
5. **Visual Dashboard**: Easy-to-understand metrics with color-coded performance indicators

## Future Enhancements

1. **AI Confidence Correlation**: Track correlation between AI confidence scores and user acceptance
2. **Time-based Analysis**: Analyze AI usage patterns over time
3. **Comparative Studies**: Compare user performance with and without AI assistance
4. **Export Functionality**: Export AI-specific analytics for research purposes
