<div class="stai-container">
  <h1>Questionnaire STAI-Y1 (É<PERSON>)</h1>
  <p>Imaginons la situation suivante : dans quelques instants, vous allez participer à une compétition ou passer un concours dont le résultat est particulièrement important pour vous et la suite de votre carrière.</p>
  <p>Lisez chaque énoncé et cochez la case qui décrit le mieux ce que vous ressentez en ce moment. Ne passez pas trop de temps sur chaque point.</p>

  <form [formGroup]="form">
    <div class="participant-info">
      <label>
        Sexe:
        <select formControlName="gender">
          <option value="">--</option>
          <option value="male">Homme</option>
          <option value="female">Femme</option>
        </select>
      </label>
      <label>
        Date du test:
        <input type="date" formControlName="testDate" />
      </label>
    </div>

    <div class="questions">
      <div *ngFor="let q of questions" class="question">
        <p>{{q.id}}. {{q.text}}</p>
        <div class="options">
          <label *ngFor="let opt of options">
            <input type="radio"
                   [formControlName]="'q' + q.id"
                   [value]="opt.value" />
            {{opt.label}}
          </label>
        </div>
      </div>
    </div>
    <button type="button" (click)="reset()">Réinitialiser</button>
    <button type="button" (click)="submit()">Soumettre</button>
  </form>
</div>
