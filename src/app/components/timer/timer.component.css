.timer-container {
  display: flex;
  flex-direction: column;
    align-items: flex-end;
  color: white;
  font-family: 'Arial', sans-serif;
  padding-right: 20px;
}

.time-display {
  font-size: 3em;
  padding-top: 10px;
  justify-self: end;
}

.top-row {
  display: grid;
  grid-template-columns: 1fr auto auto;
  align-items: center;
  width: 100%;
}

.center-button {
  justify-self: center;
  padding-top: 10px;
}

.time-left {
  font-size: 1.2rem;
  margin-top: 5px;
}

.finish {
  height: 40px;
  width: 120px;
  margin: 5px;
  background: #333;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  cursor: pointer;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: solid #404c5d 1px;
  font-size: 18px;
  color: rgb(161,161,161);
  -webkit-transition: 500ms;
  border-radius: 5px;
  background: linear-gradient(145deg, #2e2d2d, #212121);
  -webkit-box-shadow: -1px -5px 15px #41465b, 5px 5px 15px #41465b, inset 5px 5px 10px #212121, inset -5px -5px 10px #41465b;
  box-shadow: -1px -5px 15px #41465b, 5px 5px 15px #41465b, inset 5px 5px 10px #212121, inset -5px -5px 10px #41465b;
}

finish:hover {
  -webkit-box-shadow: 1px 1px 13px #20232e, -1px -1px 13px #545b78;
  box-shadow: 1px 1px 13px #20232e, -1px -1px 13px #545b78;
  color: #d6d6d6;
  -webkit-transition: 500ms;
  transition: 500ms;
}

finish:active {
  -webkit-box-shadow: 1px 1px 13px #20232e, -1px -1px 33px #545b78;
  box-shadow: 1px 1px 13px #20232e, -1px -1px 33px #545b78;
  color: #d6d6d6;
  -webkit-transition: 100ms;
  transition: 100ms;
}
