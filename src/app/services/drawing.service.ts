import { Injectable } from '@angular/core';
import {Rectangle} from "../components/spectro/spectro.component";

@Injectable({
  providedIn: 'root'
})
export class DrawingService {
  private drawingMode = false;
  private interactionEnabled = true;
  isAnnotationCreatorVisibile: boolean = true;


  setDrawingMode(mode: boolean): void {
    this.drawingMode = mode;
  }

  isDrawingMode(): boolean {
    return this.drawingMode;
  }

  setInteractionEnabled(enabled: boolean): void {
    this.interactionEnabled = enabled;
  }

  isInteractionEnabled(): boolean {
    return this.interactionEnabled;
  }

  removeRectangle(rectangleId: string): void {
    const rectangleElement = document.getElementById(rectangleId);
    if (rectangleElement) {
      rectangleElement.remove();
    }
  }

}
