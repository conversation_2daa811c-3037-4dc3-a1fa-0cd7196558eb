<div class="questionnaire-container">
  <!-- Progress indicator -->
  <div class="progress-indicator">
    <span>Page {{ currentPage + 1 }} of {{ totalPages }}</span>
  </div>

  <!-- Page 0: Welcome/Introduction -->
  <div *ngIf="currentPage === 0" class="page welcome-page">
    <h1>{{ getPageTitle() }}</h1>
    <div class="welcome-content">
      <p>Nous vous remercions d'avoir participé à notre étude. Nous aimerions maintenant vous poser quelques brèves questions sur votre charge mentale et sur la fatigue que vous avez pu ressentir pendant la tâche. Vos commentaires honnêtes nous aident à mieux comprendre à quel point l'activité a été exigeante pour vous et nous permettront d'apporter des améliorations à l'avenir.</p>

      <p>Veuillez lire attentivement chaque question et choisir l'option qui reflète le mieux ce que vous avez ressenti. Vos réponses resteront confidentielles et ne seront utilisées qu'à des fins de recherche. Si vous avez des inquiétudes, n'hésitez pas à nous faire part avant de continuer.</p>
    </div>
  </div>

  <!-- Page 1: Mental Load Questionnaire -->
  <div *ngIf="currentPage === 1" class="page questionnaire-page">
    <h2>{{ getPageTitle() }}</h2>
    <p class="instructions">Pour chaque question ci-dessous, placez une croix dans le cercle qui correspond le mieux à votre ressenti à propos de la déclaration où : 1 est extrêmement faible, 4 moyen et 7 est très élevé.</p>

    <form [formGroup]="questionnaireForms[1]" class="questionnaire-form">
      <div class="scale-header">
        <div class="question-column"></div>
        <div class="scale-options">
          <span *ngFor="let option of mentalLoadOptions" class="scale-label">{{ option.label }}</span>
        </div>
      </div>

      <div *ngFor="let question of mentalLoadQuestions" class="question-row">
        <div class="question-text">
          {{ question.id }}. {{ question.text }}
        </div>
        <div class="radio-options">
          <label *ngFor="let option of mentalLoadOptions" class="radio-option">
            <input type="radio"
                   [formControlName]="'q' + question.id"
                   [value]="option.value"
                   (change)="trackQuestionResponse('mental_load_q' + question.id, option.value, 'mental_load')" />
            <span class="radio-circle"></span>
          </label>
        </div>
      </div>
    </form>
  </div>

  <!-- Page 2: Fatigue Questionnaire -->
  <div *ngIf="currentPage === 2" class="page questionnaire-page">
    <h2>{{ getPageTitle() }}</h2>
    <p class="instructions">Pour chacune des questions ci-dessous, placez une croix dans le cercle qui caractérise le mieux ce que vous ressentez à propos de la déclaration où : 1 = fortement en désaccord, 2 = en désaccord, 3 = ni d'accord ni en désaccord, 4 = d'accord et 5 = tout à fait d'accord.</p>

    <form [formGroup]="questionnaireForms[2]" class="questionnaire-form">
      <div class="scale-header">
        <div class="question-column"></div>
        <div class="scale-options fatigue-scale">
          <span class="scale-label">Fortement en désaccord</span>
          <span class="scale-label">En désaccord</span>
          <span class="scale-label">Ni d'accord ni en désaccord</span>
          <span class="scale-label">D'accord</span>
          <span class="scale-label">Tout à fait d'accord</span>
        </div>
      </div>

      <div *ngFor="let question of fatigueQuestions" class="question-row">
        <div class="question-text">
          {{ question.id }}. {{ question.text }}
        </div>
        <div class="radio-options">
          <label *ngFor="let option of fatigueOptions" class="radio-option">
            <input type="radio"
                   [formControlName]="'q' + question.id"
                   [value]="option.value"
                   (change)="trackQuestionResponse('fatigue_q' + question.id, option.value, 'fatigue')" />
            <span class="radio-circle"></span>
          </label>
        </div>
      </div>
    </form>
  </div>

  <!-- Page 3: Self-Satisfaction Questionnaire -->
  <div *ngIf="currentPage === 3" class="page questionnaire-page">
    <h2>{{ getPageTitle() }}</h2>
    <p class="instructions">Pour chacune des questions ci-dessous, placez une croix dans le cercle qui caractérise le mieux ce que vous ressentez à propos de la déclaration où : 1 = fortement en désaccord, 2 = en désaccord, 3 = ni d'accord ni en désaccord, 4 = d'accord et 5 = tout à fait d'accord.</p>

    <form [formGroup]="questionnaireForms[3]" class="questionnaire-form">
      <div class="scale-header">
        <div class="question-column"></div>
        <div class="scale-options satisfaction-scale">
          <span class="scale-label">Fortement en désaccord</span>
          <span class="scale-label">En désaccord</span>
          <span class="scale-label">Ni d'accord ni en désaccord</span>
          <span class="scale-label">D'accord</span>
          <span class="scale-label">Tout à fait d'accord</span>
        </div>
      </div>

      <div *ngFor="let question of satisfactionQuestions" class="question-row">
        <div class="question-text">
          {{ question.id }}. {{ question.text }}
        </div>
        <div class="radio-options">
          <label *ngFor="let option of satisfactionOptions" class="radio-option">
            <input type="radio"
                   [formControlName]="'q' + question.id"
                   [value]="option.value"
                   (change)="trackQuestionResponse('satisfaction_q' + question.id, option.value, 'satisfaction')" />
            <span class="radio-circle"></span>
          </label>
        </div>
      </div>
    </form>
  </div>

  <!-- Navigation buttons -->
  <div class="navigation-buttons">
    <button *ngIf="currentPage > 0"
            class="nav-button prev-button"
            (click)="previousPage()">
      ← Précédent
    </button>

    <button *ngIf="currentPage < totalPages - 1"
            class="nav-button next-button"
            [disabled]="!canProceed()"
            (click)="nextPage()">
      Suivant →
    </button>

    <button *ngIf="currentPage === totalPages - 1"
            class="nav-button finish-button"
            [disabled]="!areAllFormsValid()"
            (click)="finishQuestionnaire()">
      Terminer
    </button>
  </div>

  <!-- Validation message -->
  <div *ngIf="currentPage > 0 && !isCurrentPageValid()" class="validation-message">
    Veuillez répondre à toutes les questions avant de continuer.
  </div>
</div>
