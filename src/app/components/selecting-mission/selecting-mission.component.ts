import {Component, OnInit} from '@angular/core';
import {SelectingMissionService} from "../../services/selecting-mission.service";
import {FormsModule} from "@angular/forms";
import {NgForOf, NgIf} from "@angular/common";
import {LocalStorageService} from "../../services/local-storage.service";
import {Router} from "@angular/router";
import {AudioService} from "../../services/audio.service";
import {GroupAssignmentService} from "../../services/group-assignment.service";


@Component({
  selector: 'app-selecting-mission',
  standalone: true,
  imports: [
    FormsModule,
    NgForOf,
    NgIf
  ],
  templateUrl: './selecting-mission.component.html',
  styleUrl: './selecting-mission.component.css'
})
export class SelectingMissionComponent implements OnInit {
  spectroNames: string[] = [];
  selectedSpectroNames: string = '';

  userNamesChoose: string[] = [];
  selectNameChoose: string = '';
  sourceType: 'user' | 'ia' = 'ia';

  // Group assignment properties
  experimentGroup: 'WITH_AI' | 'WITHOUT_AI' | null = null;

  constructor(private selectingMissionService: SelectingMissionService, private localstorageService: LocalStorageService,
              private router: Router, private audioService: AudioService, private groupAssignmentService: GroupAssignmentService) {}

  ngOnInit() {
    this.selectingMissionService.getAllSpectroNames().subscribe((data) => {
      this.spectroNames = data;
    });

    // Load existing group assignment if any
    this.experimentGroup = this.groupAssignmentService.getCurrentUserGroup();
  }

  onImageSelect() {
    this.selectingMissionService.get_user_who_made_annotation_on_specific_spectro(this.selectedSpectroNames).subscribe((data) => {
      this.userNamesChoose = data;
      this.selectNameChoose = this.userNamesChoose.length > 0 ? this.userNamesChoose[0] : '';
    }, (err) => {
      console.log("Error fetching users from selecting mission : ",err);
      this.userNamesChoose = [];
      this.selectNameChoose = '';
    })
  }

  saveSpectroName() {
    // Validate that both mission and group are selected
    if (!this.selectedSpectroNames) {
      alert('Please select a mission first.');
      return;
    }

    if (!this.experimentGroup) {
      alert('Please select an experiment group (WITH or WITHOUT AI).');
      return;
    }

    // Save mission name
    this.localstorageService.setItem('spectroName', this.selectedSpectroNames);

    // Save group assignment
    const userId = this.localstorageService.getItem('uniqueId') || 'anonymous';
    this.groupAssignmentService.setUserGroup(userId, this.experimentGroup);

    // Handle existing IA/User logic for backward compatibility
    if(this.sourceType === 'ia') {
      this.localstorageService.setItem('IAName', 'IAName');
    } else if(this.sourceType === 'user') {
      this.localstorageService.setItem('IAName', this.selectNameChoose);
    }

    alert(`Saved: ${this.selectedSpectroNames} with experiment group: ${this.experimentGroup}`);
  }

  onGroupChange() {
    // This method can be used to handle any logic when group selection changes
    console.log('Experiment group changed to:', this.experimentGroup);
  }

  goToHome() {
    this.router.navigate(['/']);
  }

}
