import { TestBed } from '@angular/core/testing';
import { ClickstreamService } from './clickstream.service';
import { LocalStorageService } from './local-storage.service';
import { ClickstreamEventType } from '../models/clickstream.models';

describe('ClickstreamService', () => {
  let service: ClickstreamService;
  let localStorageService: jasmine.SpyObj<LocalStorageService>;

  beforeEach(() => {
    const localStorageSpy = jasmine.createSpyObj('LocalStorageService', ['getItem', 'setItem', 'removeItem']);

    TestBed.configureTestingModule({
      providers: [
        ClickstreamService,
        { provide: LocalStorageService, useValue: localStorageSpy }
      ]
    });

    service = TestBed.inject(ClickstreamService);
    localStorageService = TestBed.inject(LocalStorageService) as jasmine.SpyObj<LocalStorageService>;

    // Setup default localStorage responses
    localStorageService.getItem.and.returnValue('test-user');
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should track events', () => {
    spyOn(service.events$, 'subscribe');

    service.trackEvent(ClickstreamEventType.MOUSE_CLICK, {
      elementId: 'test-button'
    });

    expect(service.events$.subscribe).toBeDefined();
  });

  it('should start annotation form tracking', () => {
    const rectangleData = {
      startX: 100,
      startY: 100,
      width: 50,
      height: 50
    };

    service.startAnnotationForm(rectangleData);

    const currentReflectionTime = service.getCurrentReflectionTime();
    expect(currentReflectionTime).toBeGreaterThan(0);
  });

  it('should end annotation form tracking', () => {
    // Start tracking first
    service.startAnnotationForm();

    // Wait a bit to ensure time passes
    setTimeout(() => {
      service.endAnnotationForm('submitted', { bruiteur: 'Hélice', trust: 75 });

      const currentReflectionTime = service.getCurrentReflectionTime();
      expect(currentReflectionTime).toBe(0); // Should be reset after ending
    }, 10);
  });

  it('should track field interactions', () => {
    service.startAnnotationForm();

    service.trackFieldFocus('bruiteur', 'initial-value');
    service.trackFieldChange('bruiteur', 'new-value', 'initial-value');
    service.trackFieldBlur('bruiteur', 'new-value');

    // The service should have tracked these interactions
    expect(service).toBeTruthy(); // Basic check that service is still functioning
  });

  it('should export session data', () => {
    const exportData = service.exportData();

    expect(exportData).toBeDefined();
    expect(exportData.events).toBeDefined();
    expect(exportData.session).toBeDefined();
  });

  it('should clear data', () => {
    service.trackEvent(ClickstreamEventType.MOUSE_CLICK);
    service.clearData();

    const exportData = service.exportData();
    expect(exportData.events.length).toBe(0);
  });
});
