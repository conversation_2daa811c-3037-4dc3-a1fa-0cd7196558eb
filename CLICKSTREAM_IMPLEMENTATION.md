# Clickstream Data Implementation with Reflection Time Tracking

This document describes the implementation of clickstream data tracking with reflection time measurement for the annotation system.

## Overview

The clickstream implementation tracks user interactions during the annotation process, with a specific focus on measuring **reflection time** - the time it takes for users to fill in annotation forms. This provides valuable insights into user behavior, decision-making patterns, and annotation efficiency.

## Key Features

### 1. Reflection Time Tracking
- **Form Opening Time**: Tracks when annotation forms are opened
- **Field Interaction Time**: Measures time spent on each form field (noise type, confidence)
- **Decision Changes**: Counts how many times users change their selections
- **Form Completion Time**: Total time from opening to submission/cancellation

### 2. AI Suggestion Tracking
- **Suggestion Display**: Tracks when AI suggestions (blue rectangles) are shown
- **Acceptance Rate**: Measures how often users accept AI suggestions
- **Rejection Tracking**: Records when users reject AI suggestions
- **Modification Tracking**: Tracks when users modify accepted AI suggestions

### 2. Comprehensive Event Tracking
- Mouse clicks and movements
- Form field focus/blur events
- Rectangle drawing start/end
- Navigation events
- Annotation lifecycle events

### 3. Data Management & Persistence
- **Configurable Retention**: Choose between session-only, persistent, or export-based data retention
- **Auto-Export**: Automatically download data when sessions end
- **Storage Management**: Monitor local storage usage and prevent overflow
- **Manual Export**: Export data at any time during the session

### 4. Real-time Analytics
- Live reflection time display
- Session statistics
- Efficiency metrics
- Trend analysis
- AI suggestion acceptance rates

## Implementation Structure

### Core Files

#### Models
- `src/app/models/clickstream.models.ts` - Data models and interfaces

#### Services
- `src/app/services/clickstream.service.ts` - Main tracking service
- `src/app/services/clickstream-storage.service.ts` - Data persistence and API communication

#### Components
- `src/app/components/reflection-analytics/reflection-analytics.component.*` - Analytics dashboard
- Enhanced `annotation-creator.component.*` - Form tracking integration
- Enhanced `spectro.component.*` - Canvas interaction tracking

### Enhanced Components

#### Annotation Creator Component
- Tracks form opening/closing events
- Monitors field interactions (focus, blur, change)
- Measures reflection time for each annotation
- Records final form submission data

#### Spectro Component
- Tracks rectangle drawing events
- Records mouse interactions on canvas
- Integrates with annotation workflow

## Data Models

### ReflectionTimeData
```typescript
interface ReflectionTimeData {
  annotationId: string;
  userId: string;
  sessionId: string;
  spectroName: string;
  
  // Time measurements
  formOpenedAt: number;
  formClosedAt: number;
  totalReflectionTime: number;
  
  // Field-specific times
  bruiteurFieldTime: number;
  confidenceFieldTime: number;
  
  // Interaction counts
  bruiteurChanges: number;
  confidenceChanges: number;
  
  // Final values and context
  finalBruiteur: string;
  finalConfidence: number;
  wasSubmitted: boolean;
  rectangleData?: object;
}
```

### ClickstreamEvent
```typescript
interface ClickstreamEvent {
  id: string;
  userId: string;
  sessionId: string;
  timestamp: number;
  eventType: ClickstreamEventType;
  elementId?: string;
  value?: any;
  metadata?: Record<string, any>;
}
```

## Usage

### 1. Automatic Tracking
The clickstream service automatically starts tracking when the application loads. No manual initialization is required.

### 2. Viewing Analytics
Navigate to `/analytics` to view the reflection time analytics dashboard, which shows:
- Real-time reflection time for current annotation
- Session summary statistics
- Detailed analytics (time distribution, field interactions)
- Individual annotation history

### 3. Data Export
- **Local Export**: Click "Export Data" to download JSON file with all analytics
- **Server Sync**: Click "Sync to Server" to upload data to backend API

### 4. Configuration
Modify tracking behavior in `ClickstreamService`:
```typescript
private config: ClickstreamConfig = {
  enableTracking: true,
  trackMouseMovements: false,
  batchSize: 50,
  flushIntervalMs: 30000,
  enableLocalStorage: true,
  apiEndpoint: 'http://localhost:8000/clickstream'
};
```

## Analytics Metrics

### Reflection Time Metrics
- **Average Reflection Time**: Mean time spent on annotations
- **Median Reflection Time**: Middle value of reflection times
- **Min/Max Reflection Time**: Fastest and slowest annotations
- **Field-specific Times**: Time spent on each form field

### Efficiency Metrics
- **Annotations per Minute**: Productivity measure
- **Hesitation Score**: Based on changes and time spent (higher = more hesitant)
- **Trend Analysis**: Whether user is getting faster or slower

### Interaction Patterns
- **Change Frequency**: How often users modify their selections
- **Most Common Choices**: Frequently selected noise types and confidence levels
- **Decision Consistency**: Patterns in user choices

## Data Storage

### Local Storage
- Events stored in `localStorage` with key `clickstream_events`
- Reflection times stored with key `reflection_times`
- Session data stored with key `annotation_session`

### Server Integration
The system is designed to work with a FastAPI backend with these endpoints:
- `POST /clickstream/events` - Submit clickstream events
- `POST /reflection-time/data` - Submit reflection time data
- `POST /clickstream/session` - Submit session data
- `GET /reflection-time/analytics` - Retrieve analytics

## Testing

Run the clickstream service tests:
```bash
ng test --include="**/clickstream.service.spec.ts"
```

## Privacy and Data Protection

- All data is anonymized using session IDs
- User identifiers are only stored locally
- No personally identifiable information is tracked
- Data can be cleared at any time through the analytics interface

## Performance Considerations

- Events are batched and sent periodically to minimize API calls
- Mouse movement tracking is disabled by default to reduce data volume
- Local storage is used as a fallback when server is unavailable
- Automatic cleanup of old data to prevent storage bloat

## Future Enhancements

1. **Machine Learning Integration**: Use reflection time data to predict annotation quality
2. **Adaptive UI**: Adjust interface based on user efficiency patterns
3. **Collaborative Analytics**: Compare performance across multiple annotators
4. **Real-time Feedback**: Provide immediate efficiency suggestions
5. **Advanced Visualizations**: Charts and graphs for reflection time trends

## Troubleshooting

### Common Issues

1. **Analytics not showing**: Check that clickstream service is properly injected
2. **Data not persisting**: Verify localStorage is enabled in browser
3. **Server sync failing**: Check API endpoint configuration and network connectivity

### Debug Mode
Enable debug logging by setting `console.log` statements in the clickstream service to monitor event tracking.

## API Integration

To integrate with your backend, implement these endpoints:

```python
# FastAPI example
@app.post("/clickstream/events")
async def receive_events(events: List[ClickstreamEvent]):
    # Store events in database
    pass

@app.post("/reflection-time/data")
async def receive_reflection_data(data: ReflectionTimeData):
    # Store reflection time data
    pass

@app.get("/reflection-time/analytics")
async def get_analytics(userId: str, spectroName: str = None):
    # Return calculated analytics
    pass
```

This implementation provides comprehensive tracking of user reflection time and interaction patterns, enabling detailed analysis of annotation behavior and efficiency optimization.
