import { Injectable } from '@angular/core';
import {HttpClient} from "@angular/common/http";
import {map, Observable} from "rxjs";

@Injectable({
  providedIn: 'root'
})
export class AudioService {
  private apiURl = 'http://localhost:8000/split-audio';
  private api = 'http://localhost:8000/get-sound-path'

  constructor(private http: HttpClient) { }

  getSplitAudio(name_image: string, startMs: number, endMs: number): Observable<string> {
    return this.http.get(`${this.apiURl}?name_image=${name_image}&start_ms=${startMs}&end_ms=${endMs}`, { responseType: 'blob' }).pipe(
      map(blob => URL.createObjectURL(blob))
    );
  }

  getSoundPath(name_image:string){
    return this.http.get(`${this.api}/${name_image}`)
  }


}
