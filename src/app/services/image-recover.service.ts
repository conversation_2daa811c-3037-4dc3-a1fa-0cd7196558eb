import { Injectable } from '@angular/core';
import {Observable} from "rxjs";
import {HttpClient} from "@angular/common/http";

export interface ISpectroRecover{
  name_spectro : string;
  image_data : string;
}


@Injectable({
  providedIn: 'root'
})
export class ImageRecoverService {
  private apiUrl = "http://localhost:8000/random-image";
  private apiUrlName = "http://localhost:8000/get_image_name";
  constructor(private http: HttpClient) { }


  getRandomSpectro() {
    return this.http.get<ISpectroRecover>(this.apiUrl)
  }

  getImageFromName(name_spectro:string){
    return this.http.get<ISpectroRecover>(`${this.apiUrlName}/${name_spectro}`);
  }
}
