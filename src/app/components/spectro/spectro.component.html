<div class="page-container">
  <app-timer></app-timer>
  <app-topbar></app-topbar>
  <div class="image-container" [class.disabled]="!zoomService.isZoomEnabled()">
    <pinch-zoom #pinchZoom [disabled]="!zoomService.isZoomEnabled()" [limit-zoom]="8">
      <canvas #imageCanvas id="imageCanvas"
              (mousemove)="onMouseMove($event)"
              (mouseleave)="resetCursorPosition()"
      (mousedown)="handleSoundClick($event)"></canvas>
    </pinch-zoom>
    <div id="cursor-info" class="cursor-info"></div>
  </div>

  <!-- Annotation Creator Component -->
  <app-annotation-creator
    [rectangleData]="currentRectangle"
    (save)="saveAnnotationCreator($event)"
    (close)="closeAnnotationCreator()"
    (delete)="deleteAnnotation()">

  </app-annotation-creator>
</div>

<div id="rect-info"></div>

<app-audio-player [startTime]="startTime" [endTime]="endTime" *ngIf="isAudioPlayerVisible" (close)="closePlayer()"></app-audio-player>

<div *ngFor="let click of middleClickPositions" class="click-dot" [ngStyle]="{left: click.x + 'px', top: click.y + 'px'}"></div>
