import {Component, EventEmitter, Input, Output, Renderer2} from '@angular/core';
import {CdkDrag} from "@angular/cdk/drag-drop";
import {FormsModule} from "@angular/forms";
import {DrawingService} from "../../services/drawing.service";
import {NgIf} from "@angular/common";
import {AnnotationService} from "../../services/annotation.service";
import {HttpParams} from "@angular/common/http";
import {ClickstreamService} from "../../services/clickstream.service";

@Component({
  selector: 'app-annotation-creator',
  standalone: true,
  imports: [
    CdkDrag,
    FormsModule,
    NgIf
  ],
  templateUrl: './annotation-creator.component.html',
  styleUrl: './annotation-creator.component.css'
})
export class AnnotationCreatorComponent {
  private resizing: boolean = false;
  private currentHandle: string | null = null;

  isAnnotationCreatorVisibile: boolean = false;
  isDisable: boolean = false;


  constructor(private renderer: Renderer2, private drawingService: DrawingService, private annotationService: AnnotationService,
              private clickstreamService: ClickstreamService) {
    this.annotationService.isAnnotationCreatorVisibile$.subscribe(state => {
      this.isAnnotationCreatorVisibile = state;

      // Track form opening/closing
      if (state) {
        this.onFormOpened();
      } else {
        this.onFormClosed();
      }
    })
  }

  // Limites size for windows
  private minWidth = 350; // Largeur minimale en pixels
  private minHeight = 300; // Hauteur minimale en pixels
  private maxWidth = 600; // Largeur maximale en pixels
  private maxHeight = 500; // Hauteur maximale en pixels

  @Input() rectangleData: any; // Données du rectangle à éditer
  @Output() save = new EventEmitter<{ bruiteur: string; trust: number;}>();
  @Output() close = new EventEmitter<void>(); // Événement déclenché à l'annulation
  @Output() delete = new EventEmitter<void>();


  startResizing(event: MouseEvent, handle: string): void {
    event.stopPropagation();
    this.resizing = true;
    this.currentHandle = handle;
    document.addEventListener('mousemove', this.onResize);
    document.addEventListener('mouseup', this.stopResizing);
  }

  onResize = (event: MouseEvent): void => {
    if (!this.resizing || !this.currentHandle) return;

    const element = document.querySelector('.annotation-creator') as HTMLElement;
    if (!element) return;

    const rect = element.getBoundingClientRect();

    // Calculer la nouvelle taille
    let newWidth = event.clientX - rect.left;
    let newHeight = event.clientY - rect.top;

    // Appliquer les limites
    newWidth = Math.max(this.minWidth, Math.min(newWidth, this.maxWidth));
    newHeight = Math.max(this.minHeight, Math.min(newHeight, this.maxHeight));

    // Appliquer la nouvelle taille à l'élément
    this.renderer.setStyle(element, 'width', `${newWidth}px`);
    this.renderer.setStyle(element, 'height', `${newHeight}px`);
  };

  stopResizing = (): void => {
    this.resizing = false;
    this.currentHandle = null;
    document.removeEventListener('mousemove', this.onResize);
    document.removeEventListener('mouseup', this.stopResizing);
  };

  onSave() {
    // Track form submission with reflection time
    this.clickstreamService.endAnnotationForm('submitted', this.rectangleData);
    this.clickstreamService.trackMouseClick('save-button');
    this.save.emit(this.rectangleData);
  }

  OnClose(): void {
    console.log('Close button clicked'); // Debug log for button press
    // Track form cancellation
    this.clickstreamService.endAnnotationForm('cancelled');
    this.clickstreamService.trackMouseClick('close-button');
    this.close.emit(); // Inform the parent to hide the form
  }

  onDelete() {
    console.log('Delete button clicked');
    // Track annotation deletion
    this.clickstreamService.endAnnotationForm('deleted');
    this.clickstreamService.trackMouseClick('delete-button');
    this.delete.emit();
  }

  // New methods for clickstream tracking
  private onFormOpened(): void {
    if (this.rectangleData) {
      console.log('📝 Form opened with rectangle data:', this.rectangleData);
      this.clickstreamService.startAnnotationForm(this.rectangleData);
    }
  }

  private onFormClosed(): void {
    // This is called when form visibility changes to false
    // The specific action (save/cancel/delete) is tracked in the respective methods
  }

  // Field interaction tracking methods
  onBruiteurFocus(): void {
    if (this.rectangleData) {
      this.clickstreamService.trackFieldFocus('bruiteur', this.rectangleData.bruiteur);
    }
  }

  onBruiteurBlur(): void {
    if (this.rectangleData) {
      this.clickstreamService.trackFieldBlur('bruiteur', this.rectangleData.bruiteur);
    }
  }

  onBruiteurChange(event: Event): void {
    if (this.rectangleData) {
      const target = event.target as HTMLSelectElement;
      const newValue = target.value;
      const oldValue = this.rectangleData.bruiteur;

      console.log('🔄 Bruiteur changed:', { oldValue, newValue, isAI: this.rectangleData.isAiSuggestion });

      this.clickstreamService.trackFieldChange('bruiteur', newValue, oldValue);
    }
  }

  onConfidenceFocus(): void {
    if (this.rectangleData) {
      this.clickstreamService.trackFieldFocus('trust', this.rectangleData.trust);
    }
  }

  onConfidenceBlur(): void {
    if (this.rectangleData) {
      this.clickstreamService.trackFieldBlur('trust', this.rectangleData.trust);
    }
  }

  onConfidenceChange(event: Event): void {
    if (this.rectangleData) {
      const target = event.target as HTMLSelectElement;
      const newValue = parseInt(target.value, 10);
      const oldValue = this.rectangleData.trust;

      console.log('🔄 Confidence changed:', { oldValue, newValue, isAI: this.rectangleData.isAiSuggestion });

      this.clickstreamService.trackFieldChange('trust', newValue, oldValue);
    }
  }
}
