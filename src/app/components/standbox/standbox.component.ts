import {AfterViewInit, Component, ElementRef} from '@angular/core';
import {LocalStorageService} from "../../services/local-storage.service";
import {Router} from "@angular/router";

@Component({
  selector: 'app-standbox',
  standalone: true,
  imports: [],
  templateUrl: './standbox.component.html',
  styleUrl: './standbox.component.css'
})
export class StandboxComponent implements AfterViewInit {
  constructor(private elementRef: ElementRef, private localService: LocalStorageService, private router: Router) { }

  ngAfterViewInit() {
    this.elementRef.nativeElement.ownerDocument.body.style.backgroundColor = 'black';
  }

  goToHome() {
    this.router.navigate(['/home'])
  }

  playSound(soundUrl: string): void {
    const audio = new Audio(soundUrl);
    audio.play();
  }

}
