.loading-screen {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #ffffff;
  text-align: center;
  padding: 20px;
}

.loading-screen h1 {
  font-size: 28px;
  font-weight: 400;
  margin-bottom: 20px;
  color: #333333;
}

.loading-screen p {
  font-size: 22px;
  font-weight: 300;
  line-height: 1.5;
  color: #333333;
  margin-bottom: 20px;
}

.loading-screen ul li {
  font-size: 22px;
  font-weight: 300;
  line-height: 1.5;
  color: #333333;
}

.btn {
  padding: 12px 24px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.btn:hover {
  background-color: #0056b3;
}


.loader {
  width: 120px;
  height: 20px;
  border-radius: 40px;
  color: #007bff;
  border: 2px solid;
  position: relative;
  overflow: hidden;
  margin-bottom: 20px;
}

.loader:before{
  content: "";
  position: absolute;
  margin: 2px;
  width: 14px;
  top: 0;
  bottom: 0;
  left: -20px;
  border-radius: inherit;
  background: black;
  box-shadow: -10px 0 12px 3px black;
  clip-path: polygon(0 5%, 100% 0, 100% 100%, 0 95%, -30px 50%);
  animation: l14 1s infinite linear;
}

@keyframes l14 {
  100% {left: calc(100% + 20px)}
}


button {
  display: flex;
  align-items: center;
  font-family: inherit;
  cursor: pointer;
  font-weight: 500;
  font-size: 17px;
  padding: 0.8em 1.3em 0.8em 0.9em;
  color: white;
  background: #ad5389;
  background: linear-gradient(to right, #0f0c29, #302b63, #24243e);
  border: none;
  letter-spacing: 0.05em;
  border-radius: 16px;
}

button svg {
  margin-right: 3px;
  transform: rotate(30deg);
  transition: transform 0.5s cubic-bezier(0.76, 0, 0.24, 1);
}

button span {
  transition: transform 0.5s cubic-bezier(0.76, 0, 0.24, 1);
}

button:hover svg {
  transform: translateX(5px) rotate(90deg);
}

button:hover span {
  transform: translateX(7px);
}
