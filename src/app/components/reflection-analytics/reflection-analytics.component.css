.analytics-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  font-family: 'Arial', sans-serif;
  background: #f5f5f5;
  min-height: 100vh;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header h2 {
  margin: 0;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.header-actions button {
  padding: 8px 16px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.header-actions button:hover {
  background: #f0f0f0;
}

.header-actions button.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.export-btn {
  background: #28a745 !important;
  color: white !important;
  border-color: #28a745 !important;
}

.sync-btn {
  background: #17a2b8 !important;
  color: white !important;
  border-color: #17a2b8 !important;
}

/* Real-time Section */
.real-time-section {
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.real-time-section h3 {
  margin: 0 0 20px 0;
  color: #333;
}

.real-time-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.stat-card {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #ddd;
  transition: all 0.3s ease;
}

.stat-card.active {
  border-left-color: #28a745;
  background: #f0fff4;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.stat-status {
  font-size: 12px;
  color: #28a745;
}

.stat-status.inactive {
  color: #666;
}

/* Summary Section */
.summary-section {
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.summary-section h3 {
  margin: 0 0 20px 0;
  color: #333;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.summary-card {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.summary-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

.summary-value {
  font-size: 28px;
  font-weight: bold;
  color: #333;
}

.summary-card.ai-summary {
  border-left: 4px solid #7DF9FF;
}

.summary-card.ai-summary .summary-value {
  color: #7DF9FF;
}

/* Detailed Section */
.detailed-section {
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.detailed-section h3 {
  margin: 0 0 20px 0;
  color: #333;
}

.analytics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.analytics-card {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.analytics-card h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
}

.time-stats, .field-stats, .choice-stats {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.time-stat, .field-stat, .choice-stat {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #e9ecef;
}

.time-stat:last-child, .field-stat:last-child, .choice-stat:last-child {
  border-bottom: none;
}

.choice-value {
  font-weight: bold;
  color: #007bff;
}

/* Click Analytics Styles */
.click-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.click-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #eee;
}

.click-stat:last-child {
  border-bottom: none;
}

.click-value {
  font-weight: bold;
  color: #28a745;
  font-size: 1.1em;
}

.ai-stats {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.ai-stat {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #e9ecef;
}

.ai-stat:last-child {
  border-bottom: none;
}

.ai-value {
  font-weight: bold;
  color: #7DF9FF;
}

.ai-accepted {
  color: #28a745;
  font-weight: bold;
}

.ai-rejected {
  color: #dc3545;
  font-weight: bold;
}

/* Annotations Section */
.annotations-section {
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.annotations-section h3 {
  margin: 0 0 20px 0;
  color: #333;
}

.annotations-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
  max-height: 400px;
  overflow-y: auto;
}

.annotation-item {
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #ddd;
}

.annotation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.annotation-number {
  font-weight: bold;
  color: #666;
}

.annotation-time {
  font-weight: bold;
  color: #333;
}

.annotation-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.annotation-status.submitted {
  background: #d4edda;
  color: #155724;
}

.annotation-status.cancelled {
  background: #f8d7da;
  color: #721c24;
}

.annotation-status.deleted {
  background: #fff3cd;
  color: #856404;
}

.annotation-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
}

.annotation-detail {
  font-size: 14px;
  color: #666;
}

/* No Data State */
.no-data {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 30px;
}

.no-data h3 {
  color: #666;
  margin-bottom: 10px;
}

.no-data p {
  color: #999;
}

/* Data Management Section */
.data-management-section {
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.data-management-section h3 {
  margin: 0 0 20px 0;
  color: #333;
}

.data-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.data-stat {
  display: flex;
  justify-content: space-between;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 4px;
  border-left: 4px solid #17a2b8;
}

.data-actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  justify-content: center;
}

.config-btn {
  padding: 10px 20px;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.config-btn:hover {
  background: #5a6268;
}

.danger-btn {
  padding: 10px 20px;
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.danger-btn:hover {
  background: #c82333;
}

/* Responsive Design */
@media (max-width: 768px) {
  .analytics-container {
    padding: 10px;
  }

  .header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .header-actions {
    flex-wrap: wrap;
    justify-content: center;
  }

  .real-time-stats,
  .summary-grid,
  .analytics-grid {
    grid-template-columns: 1fr;
  }

  .annotation-header {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }

  .annotation-details {
    grid-template-columns: 1fr;
  }
}

/* AI Performance Section */
.ai-performance-section {
  margin-top: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.ai-performance-section h4 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 18px;
}

.performance-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.performance-card {
  padding: 20px;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.performance-card.acceptance {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  border-left: 4px solid #28a745;
}

.performance-card.modification {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border-left: 4px solid #ffc107;
}

.performance-card.rejection {
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
  border-left: 4px solid #dc3545;
}

.performance-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.performance-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 15px;
}

.performance-card.acceptance .performance-value {
  color: #28a745;
}

.performance-card.modification .performance-value {
  color: #ffc107;
}

.performance-card.rejection .performance-value {
  color: #dc3545;
}

.performance-bar {
  width: 100%;
  height: 8px;
  background: rgba(0,0,0,0.1);
  border-radius: 4px;
  overflow: hidden;
}

.performance-fill {
  height: 100%;
  transition: width 0.3s ease;
  border-radius: 4px;
}

.acceptance-fill {
  background: #28a745;
}

.modification-fill {
  background: #ffc107;
}

.rejection-fill {
  background: #dc3545;
}

/* Enhanced AI value colors */
.ai-value.success {
  color: #28a745;
}

.ai-value.warning {
  color: #ffc107;
}

.ai-value.error {
  color: #dc3545;
}
