<label for="spectroNameSelect">Select une mission : </label>
<select id="spectroNameSelect" [(ngModel)]="selectedSpectroNames" (change)="onImageSelect()">
  <option *ngFor="let spectroName of spectroNames" [value]="spectroName">
    {{ spectroName}}
  </option>
</select>
<br><br>

<!-- Experiment Group Selection -->
<div class="experiment-group-selection">
  <h3>Experiment Group Selection:</h3>
  <label>
    <input type="radio" name="experimentGroup" value="WITH_AI" [(ngModel)]="experimentGroup" (change)="onGroupChange()">
    WITH ARTIFICIAL INTELLIGENCE
  </label>
  <br>
  <label>
    <input type="radio" name="experimentGroup" value="WITHOUT_AI" [(ngModel)]="experimentGroup" (change)="onGroupChange()">
    WITHOUT ARTIFICIAL INTELLIGENCE
  </label>
</div>
<br>

<!-- Legacy Source Type Selection (kept for backward compatibility) -->
<div class="legacy-selection" *ngIf="sourceType">
  <h4>Legacy Selection:</h4>
  <label>
    <input type="radio" name="sourceType" value="ia" [(ngModel)]="sourceType"> Ia
  </label>
  <label>
    <input type="radio" name="sourceType" value="user" [(ngModel)]="sourceType"> Utilisateur
  </label>
</div>

<div *ngIf="sourceType === 'user' && userNamesChoose.length > 0">
  <label for="userNameSelect">Select un utilisateur IA : </label>
  <select id="userNameSelect" [(ngModel)]="selectNameChoose">
    <option *ngFor="let userChooseName of userNamesChoose" [value]="userChooseName">
      {{ userChooseName}}
    </option>
  </select>
</div>


<br>
<button (click)="saveSpectroName()">Save</button>
<br>
<br>
<button (click)="goToHome()">Go to home</button>
