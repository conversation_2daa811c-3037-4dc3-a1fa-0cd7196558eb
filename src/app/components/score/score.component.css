.title-score {
  text-align: center;
  color: white;
}

.home-button {
  width: 60px;
  height: 60px;
  background-color: #4a90e2;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  position: relative;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.home-button:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
}

.icon-container svg {
  width: 30px;
  height: 30px;
  fill: white;
  transition: fill 0.3s ease-in-out;
}

.home-button:hover .icon-container svg {
  fill: #f1f1f1;
}

.btn-back {
  align-items: center;
  justify-content: center;
  display: flex;
}

.finish {
  font-size: 24px;
  color: white;
  text-align: center;
}

.chart-container {
  width: 50%;
  max-width: 1000px;
  margin: auto;
  max-height: 850px;
}
