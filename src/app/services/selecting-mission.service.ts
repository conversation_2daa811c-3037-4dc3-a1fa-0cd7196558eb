import { Injectable } from '@angular/core';
import {HttpClient} from "@angular/common/http";

@Injectable({
  providedIn: 'root'
})
export class SelectingMissionService {
  private apiUrl = 'http://localhost:8000/get_all_spectro_name/';
  private apirURlUser_spectro = 'http://localhost:8000/user_spectro';
  private apiURLResponse = 'http://localhost:8000/response?name_spectro';

  constructor(private http: HttpClient) { }

  getAllSpectroNames(){
    return this.http.get<string[]>(this.apiUrl);
  }

  get_user_who_made_annotation_on_specific_spectro(name_spectro: string) {
    return this.http.get<string[]>(`${this.apirURlUser_spectro}/${name_spectro}`);
  }



}
