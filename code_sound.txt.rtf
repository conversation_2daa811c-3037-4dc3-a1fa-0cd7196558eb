{\rtf1\ansi\ansicpg1252\cocoartf2821
\cocoatextscaling0\cocoaplatform0{\fonttbl\f0\fmodern\fcharset0 Courier;\f1\fmodern\fcharset0 Courier-Oblique;}
{\colortbl;\red255\green255\blue255;\red185\green101\blue173;\red23\green23\blue26;\red174\green176\blue183;
\red195\green123\blue90;\red164\green160\blue78;\red89\green158\blue96;\red71\green149\blue242;\red38\green157\blue169;
\red103\green107\blue114;\red185\green101\blue172;\red153\green168\blue186;\red203\green170\blue101;\red172\green172\blue172;
}
{\*\expandedcolortbl;;\csgenericrgb\c72549\c39608\c67843;\csgenericrgb\c9020\c9020\c10196;\csgenericrgb\c68235\c69020\c71765;
\csgenericrgb\c76471\c48235\c35294;\csgenericrgb\c64314\c62745\c30588;\csgenericrgb\c34902\c61961\c37647;\csgenericrgb\c27843\c58431\c94902;\csgenericrgb\c14902\c61569\c66275;
\csgenericrgb\c40392\c41961\c44706;\csgenericrgb\c72549\c39608\c67451;\csgenericrgb\c60000\c65882\c72941;\csgenericrgb\c79608\c66667\c39608;\csgenericrgb\c67451\c67451\c67451;
}
\margl1440\margr1440\vieww28300\viewh17700\viewkind0
\pard\tx720\tx1440\tx2160\tx2880\tx3600\tx4320\tx5040\tx5760\tx6480\tx7200\tx7920\tx8640\pardirnatural\partightenfactor0

\f0\fs26 \cf2 \cb3 middleClickPositions\cf4 : \{ \cf2 x\cf4 : \cf5 number\cf4 ; \cf2 y\cf4 : \cf5 number \cf4 \}[] = [];\
\pard\tx720\tx1440\tx2160\tx2880\tx3600\tx4320\tx5040\tx5760\tx6480\tx7200\tx7920\tx8640\pardirnatural\partightenfactor0
\cf6 @ViewChild\cf4 (\cf7 'audioPlayer'\cf4 , \{ \cf2 static\cf4 : \cf5 true \cf4 \}) \cf2 audioRef\cf4 !: ElementRef<HTMLAudioElement>;\
\
\
\pard\tx560\tx1120\tx1680\tx2240\tx2800\tx3360\tx3920\tx4480\tx5040\tx5600\tx6160\tx6720\pardirnatural\partightenfactor0
\cf8 \
\
handleSoundClick\cf4 (event: MouseEvent): \cf5 void \cf4 \{\
  event.\cf8 preventDefault\cf4 ();\
\
  \cf5 if \cf4 (event.\cf2 button \cf4 !== \cf9 1\cf4 ) \cf5 return\cf4 ; \cf10 // Ensure it's the middle button\
\
  
\f1\i \cf11 console
\f0\i0 \cf4 .\cf8 log\cf4 (\cf7 'Sound click:'\cf4 , event.\cf2 clientX\cf4 , event.\cf2 clientY\cf4 );\
\
  \cf10 // Only add a new position when explicitly requested\
  \cf5 if \cf4 (\cf5 this\cf4 .\cf2 middleClickPositions\cf4 .\cf2 length \cf4 < \cf9 2\cf4 ) \{\
    \cf5 this\cf4 .\cf2 middleClickPositions\cf4 .\cf8 push\cf4 (\{ \cf2 x\cf4 : event.\cf2 clientX\cf4 , \cf2 y\cf4 : event.\cf2 clientY \cf4 \});\
    
\f1\i \cf11 console
\f0\i0 \cf4 .\cf8 log\cf4 (\cf7 `Click \cf4 $\{\cf5 this\cf4 .\cf2 middleClickPositions\cf4 .\cf2 length\cf4 \}\cf7 : X = \cf4 $\{event.\cf2 clientX\cf4 \}\cf7 , Y = \cf4 $\{event.\cf2 clientY\cf4 \}\cf7 `\cf4 );\
  \}\
\
  \cf10 // If two clicks are captured, log them\
  \cf5 if \cf4 (\cf5 this\cf4 .\cf2 middleClickPositions\cf4 .\cf2 length \cf4 === \cf9 2\cf4 ) \{\
    
\f1\i \cf11 console
\f0\i0 \cf4 .\cf8 log\cf4 (\cf7 'Two clicks captured:'\cf4 , \cf5 this\cf4 .\cf2 middleClickPositions\cf4 );\
    \cf5 this\cf4 .\cf8 playAudioBetweenBeats\cf4 ();\
  \}\
\}\
\
\cf8 playAudioBetweenBeats\cf4 (): \cf5 void \cf4 \{\
  
\f1\i \cf11 console
\f0\i0 \cf4 .\cf8 log\cf4 (\cf7 'Playing audio between beats'\cf4 );\
  \cf5 const \cf12 audio \cf4 = \cf5 this\cf4 .\cf2 audioRef\cf4 .\cf2 nativeElement\cf4 ;\
\
  \cf10 // Convert X positions to a percentage of the screen width\
  \cf5 const \cf12 canvasRect \cf4 = \cf5 this\cf4 .\cf2 canvasRef\cf4 .\cf2 nativeElement\cf4 .\cf8 getBoundingClientRect\cf4 ();\
  \cf5 const \cf12 newScale \cf4 = \cf5 this\cf4 .\cf2 zoomLevel\cf4 ;\
\
  \cf5 const \cf12 beat1 \cf4 = (\cf5 this\cf4 .\cf2 middleClickPositions\cf4 [\cf9 0\cf4 ].
\f1\i \cf11 y 
\f0\i0 \cf4 - \cf12 canvasRect\cf4 .\cf2 top\cf4 ) / \cf12 newScale\cf4 ;\
  \cf5 const \cf12 beat2 \cf4 = (\cf5 this\cf4 .\cf2 middleClickPositions\cf4 [\cf9 1\cf4 ].
\f1\i \cf11 y 
\f0\i0 \cf4 - \cf12 canvasRect\cf4 .\cf2 top\cf4 ) / \cf12 newScale\cf4 ;\
\
  \cf5 const \cf12 beatTime1 \cf4 = \cf5 this\cf4 .\cf2 maxTime \cf4 * (\cf9 1 \cf4 - \cf12 beat1 \cf4 / \cf5 this\cf4 .\cf2 canvas\cf4 .\cf2 height\cf4 ) / \cf9 1000\cf4 ;\
  \cf5 const \cf12 beatTime2 \cf4 = \cf5 this\cf4 .\cf2 maxTime \cf4 * (\cf9 1 \cf4 - \cf12 beat2 \cf4 / \cf5 this\cf4 .\cf2 canvas\cf4 .\cf2 height\cf4 ) / \cf9 1000 \cf4 ;\
\
  
\f1\i \cf11 console
\f0\i0 \cf4 .\cf8 log\cf4 (\cf7 `Playing from \cf4 $\{\cf12 beatTime1\cf4 .\cf8 toFixed\cf4 (\cf9 2\cf4 )\}\cf7 s to \cf4 $\{\cf12 beatTime2\cf4 .\cf8 toFixed\cf4 (\cf9 2\cf4 )\}\cf7 s`\cf4 );\
\
  \cf12 audio\cf4 .\cf2 currentTime \cf4 = \cf12 beatTime1\cf4 ; \cf10 // Start at first beat\
  \cf12 audio\cf4 .\cf2 volume \cf4 = \cf9 1.0\cf4 ; \cf10 // Ensure full volume\
  \cf12 audio\cf4 .\cf8 play\cf4 ();\
\
  
\f1\i \cf11 console
\f0\i0 \cf4 .\cf8 log\cf4 (\cf7 'Play music ?'\cf4 );\
\
  \cf10 // Stop at second beat\
   \cf8 setTimeout\cf4 (() => \{\
     \cf5 if \cf4 (\cf12 audio\cf4 .\cf2 currentTime \cf4 >= \cf12 beatTime2\cf4 ) \{\
       \cf12 audio\cf4 .\cf8 pause\cf4 ();\
       \cf12 audio\cf4 .\cf2 currentTime \cf4 = \cf9 0\cf4 ; \cf10 // Reset to start\
       
\f1\i \cf11 console
\f0\i0 \cf4 .\cf8 log\cf4 (\cf7 'Audio stopped at'\cf4 , \cf12 beatTime2\cf4 , \cf7 's'\cf4 );\
     \}\
   \});\
  \cf5 this\cf4 .\cf2 middleClickPositions \cf4 = []; \cf10 // Reset for next interaction\
\cf4 \}\
\
\
\cf13 <audio \cf14 #\cf12 audioPlayer\cf13 >\
  <source \cf14 src\cf7 ="music/Ril\'e8s_Dontreallyknow.mp3" \cf14 type\cf7 ="audio/mpeg"\cf13 >\
  \cf4 Your browser does not support the audio element.\
\cf13 </audio>\
\
\cf4 \
}