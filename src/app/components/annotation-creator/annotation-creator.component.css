.annotation-creator {
  width: 300px;
  padding: 15px;
  border: 1px solid #ccc;
  border-radius: 5px;
  background-color: #f9f9f9;
  position: fixed;
  resize: none;
  overflow: auto;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  top: 10px;
  left: 10px;
  z-index: auto;
}

.header {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: move;
  position: relative; /* Pour un meilleur positionnement du bouton */
}

.close-btn {
  background-color: red;
  border: none;
  color: white;
  font-size: 12px; /* Réduction de la taille */
  font-weight: bold;
  width: 20px; /* Plus petit */
  height: 20px; /* Plus petit */
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  right: 5%; /* Positionné exactement à droite */
  top: 8%; /* Centré verticalement par rapport à la ligne */
  transform: translateY(-50%); /* Ajuste l'alignement vertical */
  padding: 0;
}

.close-btn:hover {
  background-color: darkred;
}

.minimize-btn{
  width: 20px;
  height: 20px;
  font-size: 18px; /* Réduction de la taille */
  font-weight: bold;
  background-color: orange;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  position: absolute;
  cursor: pointer;
  transition: transform 0.2s ease-in-out;
    top: 1.75%;
  right: 15% ;
}

.minimize-btn:hover {
  transform: scale(1.1);
}

.form-group {
  margin-bottom: 10px;
}

label {
  display: block;
  font-size: 14px;
  margin-bottom: 5px;
}

input,
textarea {
  width: 100%;
  padding: 5px;
  border: 1px solid #ccc;
  border-radius: 3px;
  box-sizing: border-box;
}

textarea {
  resize: none;
}

.save-btn {
  padding: 8px 12px;
  border: none;
  border-radius: 3px;
  background-color: #007bff;
  color: white;
  cursor: pointer;
}

.save-btn:hover {
  background-color: #0056b3;
}

.resize-handle {
  position: absolute;
  width: 10px;
  height: 10px;
  background-color: gray;
  cursor: se-resize; /* Curseur de redimensionnement */
  z-index: 10;
}

.resize-handle.bottom-right {
  bottom: 0;
  right: 0;
  transform: translate(50%, 50%);
}

.delete-annotation {
  font-size: 12px; /* Réduction de la taille */
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  right: 5%; /* Positionné exactement à droite */
  bottom: 8%; /* Centré verticalement par rapport à la ligne */
  padding: 0;
}
